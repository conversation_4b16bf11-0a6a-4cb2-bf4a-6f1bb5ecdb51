# TODO

## Next

- make the reports more generic
- icp analysis without research the company description
- re-research company description
- post single job url
- company create date in frontend
- pipedrive loswerden (Entwurf: wie will ich wirklich arbeiten)
- search term exclusion (react native)

## Backlog

- run through complexity analysis and cleanup
- more structured icp_rating descriptions

## CRM Workflow

1. company comes in by search result
2. Qualify yes: fit, no: no-fit (ignore with description)
3. "Qualified": research contacts, contact them (linkedin)
4. "Contacted" with date
5. "No Reaction"
6. "Lost": with description

Statuses companies can have:

- NEW
- QUALIFIED / NO_FIT
- CONTACTED
- NO_REACTION
- LOST

Each transition with an optional comment
This is a workflow transition, so keep the history in a separate table