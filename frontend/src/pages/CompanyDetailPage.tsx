import React, {useEffect, useState} from 'react'
import {useParams} from 'react-router-dom'
import {companyApi, jobApi, personApi} from '../services/api'
import {Company, Person} from '../types'
import useApi from '../hooks/useApi'
import {
    Card,
    CompanyCard,
    ContactsTable,
    Job,
    JobsTable,
    LoadingSpinner,
    Modal,
    PageHeader,
    TabItem,
    Tabs,
    useLayoutOutletContext
} from '../components'
import {getIcpRatingClass, markdownToHtml} from "@/utils/helpers.ts";

const CompanyDetailPage: React.FC = () => {
    const {id} = useParams<{ id: string }>()
    const {addFlashMessage, showLoadingModal, hideLoadingModal} = useLayoutOutletContext()

    // State for research loading and errors
    const [isResearching, setIsResearching] = useState(false)
    const [researchError, setResearchError] = useState<string | null>(null)
    const [researchingPersonId, setResearchingPersonId] = useState<number | null>(null)

    // State for ignore modal
    const [isIgnoreModalOpen, setIsIgnoreModalOpen] = useState(false)
    const [ignoreReason, setIgnoreReason] = useState('')

    // State for add job by URL modal
    const [isAddJobModalOpen, setIsAddJobModalOpen] = useState(false)
    const [jobUrl, setJobUrl] = useState('')
    const [jobTitle, setJobTitle] = useState('')
    const [jobDescription, setJobDescription] = useState('')

    // State for crawl career page modal
    const [isCrawlCareerPageModalOpen, setIsCrawlCareerPageModalOpen] = useState(false)
    const [careerPageUrl, setCareerPageUrl] = useState('')
    const [isCrawling, setIsCrawling] = useState(false)

    // State for delete job confirmation modal
    const [isDeleteJobModalOpen, setIsDeleteJobModalOpen] = useState(false)
    const [jobToDelete, setJobToDelete] = useState<number | string | null>(null)

    // State for delete all jobs confirmation modal
    const [isDeleteAllJobsModalOpen, setIsDeleteAllJobsModalOpen] = useState(false)

    // State for tracking which job description is being cleaned
    const [jobBeingCleaned, setJobBeingCleaned] = useState<number | string | null>(null)

    // State for Pipedrive lead
    const [hasLead, setHasLead] = useState(false)
    const [leadId, setLeadId] = useState<number | null>(null)
    const [isLeadArchived, setIsLeadArchived] = useState(false)

    // State for Chat tab
    const [chatQuery, setChatQuery] = useState('')
    const [chatResponse, setChatResponse] = useState('')
    const [chatContext, setChatContext] = useState('')
    const [isShowingContext, setIsShowingContext] = useState(false)
    const [isChatLoading, setIsChatLoading] = useState(false)
    const [selectedJobIds, setSelectedJobIds] = useState<number[]>([])
    const [selectedPersonIds, setSelectedPersonIds] = useState<number[]>([])
    const [previewContext, setPreviewContext] = useState('')
    const [isPreviewingContext, setIsPreviewingContext] = useState(false)
    const [isLoadingPreview, setIsLoadingPreview] = useState(false)

    // State for inline editing of company profile fields
    const [isEditingWhatTheyDo, setIsEditingWhatTheyDo] = useState(false)
    const [isEditingCurrentWork, setIsEditingCurrentWork] = useState(false)
    const [isEditingHowToHelp, setIsEditingHowToHelp] = useState(false)
    const [editedWhatTheyDo, setEditedWhatTheyDo] = useState('')
    const [editedCurrentWork, setEditedCurrentWork] = useState('')
    const [editedHowToHelp, setEditedHowToHelp] = useState('')

    // State for statuses and workflow history
    const [statuses, setStatuses] = useState<any[]>([])
    const [workflowHistory, setWorkflowHistory] = useState<any[]>([])
    const [selectedStatus, setSelectedStatus] = useState<string>('')
    const [statusComment, setStatusComment] = useState<string>('')
    const [isUpdatingStatus, setIsUpdatingStatus] = useState(false)

    // API hooks
    const getCompany = useApi(companyApi.getCompany)
    const searchJobs = useApi(companyApi.searchJobs, {
        onSuccess: (data) => {
            addFlashMessage('success', `Found ${data.job_count} jobs for ${getCompany.data?.name}`)
        },
        onError: (error) => {
            addFlashMessage('error', `Failed to search jobs: ${error.message}`)
        }
    })
    const addJobByUrl = useApi(companyApi.addJobByUrl, {
        onSuccess: () => {
            addFlashMessage('success', `Job added successfully for ${getCompany.data?.name}`)
            setIsAddJobModalOpen(false)
            // Reset form fields
            setJobUrl('')
            setJobTitle('')
            setJobDescription('')
        },
        onError: (error) => {
            addFlashMessage('error', `Failed to add job: ${error.message}`)
        }
    })
    const crawlCareerPage = useApi(companyApi.crawlCareerPage, {
        onSuccess: () => {
            addFlashMessage('success', `Career page crawl initiated for ${getCompany.data?.name}`)
            // Reset form field
            setCareerPageUrl('')
        },
        onError: (error) => {
            addFlashMessage('error', `Failed to crawl career page: ${error.message}`)
        }
    })
    const reanalyzeCompany = useApi(companyApi.reanalyzeCompany, {
        onSuccess: (data) => {
            addFlashMessage('success', `Company ${data.company.name} reanalyzed successfully`)
            // Refresh company data
            getCompany.execute(Number(id))
        },
        onError: (error) => {
            addFlashMessage('error', `Failed to reanalyze company: ${error.message}`)
        }
    })

    const reanalyzeCompanySkipDescription = useApi(companyApi.reanalyzeCompanySkipDescription, {
        onSuccess: (data) => {
            addFlashMessage('success', `Company ${data.company.name} reanalyzed successfully (skipped description research)`)
            // Refresh company data
            getCompany.execute(Number(id))
        },
        onError: (error) => {
            addFlashMessage('error', `Failed to reanalyze company: ${error.message}`)
        }
    })

    const researchCompanyDescription = useApi(companyApi.researchCompanyDescription, {
        onSuccess: (data) => {
            addFlashMessage('success', `Company ${data.company.name} description researched successfully`)
            // Refresh company data
            getCompany.execute(Number(id))
        },
        onError: (error) => {
            addFlashMessage('error', `Failed to research company description: ${error.message}`)
        }
    })

    const sendChatQuery = useApi(companyApi.chat, {
        onSuccess: (data) => {
            setChatResponse(data.response)
            setChatContext(data.context || '')
            setIsChatLoading(false)
        },
        onError: (error) => {
            addFlashMessage('error', `Failed to get chat response: ${error.message}`)
            setIsChatLoading(false)
        }
    })
    const syncPipedrive = useApi(companyApi.syncPipedrive, {
        onSuccess: (data) => {
            addFlashMessage('success', `Company ${data.company.name} synced to Pipedrive successfully`)
            // Refresh company data
            getCompany.execute(Number(id))
        },
        onError: (error) => {
            addFlashMessage('error', `Failed to sync to Pipedrive: ${error.message}`)
        }
    })
    const disconnectPipedrive = useApi(companyApi.disconnectPipedrive, {
        onSuccess: (data) => {
            addFlashMessage('success', `Company ${data.company.name} disconnected from Pipedrive successfully`)
            // Refresh company data
            getCompany.execute(Number(id))
        },
        onError: (error) => {
            addFlashMessage('error', `Failed to disconnect from Pipedrive: ${error.message}`)
        }
    })
    const syncPipedriveContacts = useApi(companyApi.syncPipedriveContacts, {
        onSuccess: (data) => {
            addFlashMessage('success', data.message)
            // Refresh company data
            getCompany.execute(Number(id))
        },
        onError: (error) => {
            addFlashMessage('error', `Failed to sync contacts from Pipedrive: ${error.message}`)
        }
    })
    const researchKeyDecisionMakers = useApi(companyApi.researchKeyDecisionMakers, {
        onSuccess: (data) => {
            addFlashMessage('success', data.message || `Key decision makers researched successfully`)
            setIsResearching(false)
            setResearchError(null)
        },
        onError: (error) => {
            setResearchError(error.message)
            addFlashMessage('error', `Failed to research key decision makers: ${error.message}`)
            setIsResearching(false)
        }
    })

    const addPersonToPipedrive = useApi(personApi.addToPipedrive, {
        onSuccess: (data) => {
            addFlashMessage('success', data.message || 'Person added to Pipedrive successfully')
            // Refresh company data to update the list of contacts
            getCompany.execute(Number(id))
        },
        onError: (error) => {
            addFlashMessage('error', `Failed to add person to Pipedrive: ${error.message}`)
        }
    })

    const savePipedriveContact = useApi(companyApi.savePipedriveContact, {
        onSuccess: (data) => {
            addFlashMessage('success', data.message || 'Contact saved to local database successfully')
            // Refresh company data to update the list of contacts
            getCompany.execute(Number(id))
        },
        onError: (error) => {
            addFlashMessage('error', `Failed to save contact to local database: ${error.message}`)
        }
    })

    const deletePerson = useApi(personApi.deletePerson, {
        onSuccess: (data) => {
            addFlashMessage('success', data.message || 'Person deleted successfully')
            // Refresh company data to update the list of contacts
            getCompany.execute(Number(id))
        },
        onError: (error) => {
            addFlashMessage('error', `Failed to delete person: ${error.message}`)
        }
    })

    const deletePipedrivePerson = useApi(personApi.deletePipedrivePerson, {
        onSuccess: (data) => {
            addFlashMessage('success', data.message || 'Person deleted from Pipedrive successfully')
            // Refresh company data to update the list of contacts
            getCompany.execute(Number(id))
        },
        onError: (error) => {
            addFlashMessage('error', `Failed to delete person from Pipedrive: ${error.message}`)
        }
    })

    const deleteJob = useApi(jobApi.deleteJob, {
        onSuccess: (data) => {
            addFlashMessage('success', data.message || 'Job deleted successfully')
            // Refresh company data to update the list of jobs
            getCompany.execute(Number(id))
        },
        onError: (error) => {
            addFlashMessage('error', `Failed to delete job: ${error.message}`)
        }
    })

    const deleteAllJobs = useApi(jobApi.deleteAllJobs, {
        onSuccess: (data) => {
            addFlashMessage('success', data.message || 'All jobs deleted successfully')
            // Refresh company data to update the list of jobs
            getCompany.execute(Number(id))
        },
        onError: (error) => {
            addFlashMessage('error', `Failed to delete all jobs: ${error.message}`)
        }
    })

    const cleanJobDescription = useApi(jobApi.cleanJobDescription, {
        onSuccess: (data) => {
            addFlashMessage('success', data.message || 'Job description cleaned successfully')
            // Refresh company data to update the job description
            getCompany.execute(Number(id))
            setJobBeingCleaned(null)
        },
        onError: (error) => {
            console.error('Error cleaning job description:', error)
            addFlashMessage('error', `Failed to clean job description: ${error.message}`)
            setJobBeingCleaned(null)
        }
    })

    const researchPerson = useApi(personApi.researchPerson, {
        onSuccess: (data) => {
            addFlashMessage('success', data.message || 'Person researched successfully')
            // Refresh company data to update the list of contacts
            getCompany.execute(Number(id))
            // Reset researching person ID
            setResearchingPersonId(null)
        },
        onError: (error) => {
            addFlashMessage('error', `Failed to research person: ${error.message}`)
            // Reset researching person ID on error
            setResearchingPersonId(null)
        }
    })

    const ignoreCompany = useApi(companyApi.ignoreCompany, {
        onSuccess: (data) => {
            addFlashMessage('success', data.message || `Company ${data.company.name} ignored successfully`)
            // Refresh company data
            getCompany.execute(Number(id))
        },
        onError: (error) => {
            addFlashMessage('error', `Failed to ignore company: ${error.message}`)
        }
    })

    const unignoreCompany = useApi(companyApi.unignoreCompany, {
        onSuccess: (_data) => {
            // Use company name from the main data source instead of the response data
            const companyName = getCompany.data?.name || 'Company';
            addFlashMessage('success', `Company ${companyName} unignored successfully`)
            // Refresh company data
            getCompany.execute(Number(id))
        },
        onError: (error) => {
            addFlashMessage('error', `Failed to unignore company: ${error.message}`)
        }
    })

    // Local state for company data that can be updated without a full reload
    const [localCompanyData, setLocalCompanyData] = useState<Company | null>(null)

    // API hook for updating company profile fields
    const updateCompanyProfile = useApi(companyApi.updateCompany, {
        onSuccess: (_data) => {
            addFlashMessage('success', `Company profile updated successfully`)
            // Update local state with the new data instead of reloading
            if (getCompany.data) {
                // Create a new company object with updated fields
                const updatedCompany = {
                    ...getCompany.data,
                    ..._data.company
                }
                // Update the local company data state
                setLocalCompanyData(updatedCompany)
            }
            // Reset editing states
            setIsEditingWhatTheyDo(false)
            setIsEditingCurrentWork(false)
            setIsEditingHowToHelp(false)
        },
        onError: (error) => {
            addFlashMessage('error', `Failed to update company profile: ${error.message}`)
        }
    })

    // Pipedrive lead API hooks
    const checkLead = useApi(companyApi.checkLead, {
        onSuccess: (data) => {
            setHasLead(data.has_lead)
            setLeadId(data.lead?.id || null)
            setIsLeadArchived(data.is_archived || false)
        },
        onError: (error) => {
            addFlashMessage('error', `Failed to check lead status: ${error.message}`)
            setHasLead(false)
            setLeadId(null)
            setIsLeadArchived(false)
        }
    })

    const createLead = useApi(companyApi.createLead, {
        onSuccess: (data) => {
            addFlashMessage('success', data.message || 'Lead created successfully')
            setHasLead(true)
            setLeadId(data.lead?.id || null)
            // Refresh company data
            getCompany.execute(Number(id))
        },
        onError: (error) => {
            addFlashMessage('error', `Failed to create lead: ${error.message}`)
        }
    })

    const archiveLead = useApi(companyApi.archiveLead, {
        onSuccess: (data) => {
            addFlashMessage('success', data.message || 'Lead archived successfully')
            // Refresh lead status
            checkLead.execute(Number(id))
            // Refresh company data
            getCompany.execute(Number(id))
        },
        onError: (error) => {
            addFlashMessage('error', `Failed to archive lead: ${error.message}`)
        }
    })

    const unarchiveLead = useApi(companyApi.unarchiveLead, {
        onSuccess: (data) => {
            addFlashMessage('success', data.message || 'Lead unarchived successfully')
            // Refresh lead status
            checkLead.execute(Number(id))
            // Refresh company data
            getCompany.execute(Number(id))
        },
        onError: (error) => {
            addFlashMessage('error', `Failed to unarchive lead: ${error.message}`)
        }
    })

    // Fetch company data on mount
    useEffect(() => {
        if (id) {
            getCompany.execute(Number(id))
        }
    }, [id])

    // Check lead status when company data is loaded and has Pipedrive ID
    useEffect(() => {
        if (getCompany.data?.pipedrive_org_id) {
            checkLead.execute(Number(id))
        } else {
            setHasLead(false)
            setLeadId(null)
        }
    }, [getCompany.data?.pipedrive_org_id, id])

    // Update localCompanyData when getCompany.data changes
    useEffect(() => {
        if (getCompany.data) {
            setLocalCompanyData(getCompany.data)
        }
    }, [getCompany.data])

    // Load statuses and workflow history
    useEffect(() => {
        if (id) {
            companyApi.getCompanyStatuses().then(response => {
                setStatuses(response.data.statuses)
            })
            companyApi.getCompanyWorkflowHistory(Number(id)).then(response => {
                setWorkflowHistory(response.data.transitions)
            })
        }
    }, [id])

    // Update status when company data changes
    useEffect(() => {
        if (getCompany.data?.status_id) {
            const status = statuses.find(s => s.id === getCompany.data.status_id)
            if (status) {
                setSelectedStatus(status.name)
            }
        }
    }, [getCompany.data?.status_id, statuses])

    // Handle search jobs
    const handleSearchJobs = async () => {
        if (!id) return

        showLoadingModal()
        try {
            await searchJobs.execute(Number(id))
        } finally {
            hideLoadingModal()
        }
    }

    // Handle reanalyze company
    const handleReanalyze = async () => {
        if (!id) return

        showLoadingModal()
        try {
            await reanalyzeCompany.execute(Number(id))
        } finally {
            hideLoadingModal()
        }
    }

    // Handle reanalyze company without researching description
    const handleReanalyzeSkipDescription = async () => {
        if (!id) return

        showLoadingModal()
        try {
            await reanalyzeCompanySkipDescription.execute(Number(id))
        } finally {
            hideLoadingModal()
        }
    }

    // Handle research company description
    const handleResearchDescription = async () => {
        if (!id) return

        showLoadingModal()
        try {
            await researchCompanyDescription.execute(Number(id))
        } finally {
            hideLoadingModal()
        }
    }

    // Handle sync to Pipedrive
    const handleSyncPipedrive = async () => {
        if (!id) return

        showLoadingModal()
        try {
            await syncPipedrive.execute(Number(id))
        } finally {
            hideLoadingModal()
        }
    }

    // Handle disconnect from Pipedrive
    const handleDisconnectPipedrive = async () => {
        if (!id) return

        // Confirm before disconnecting
        if (!window.confirm('Are you sure you want to disconnect this company from Pipedrive? This will remove the connection but will not delete the organization in Pipedrive.')) {
            return
        }

        showLoadingModal()
        try {
            await disconnectPipedrive.execute(Number(id))
        } finally {
            hideLoadingModal()
        }
    }

    // Handle sync Pipedrive contacts
    const handleSyncPipedriveContacts = async () => {
        if (!id) return

        showLoadingModal()
        try {
            await syncPipedriveContacts.execute(Number(id))
        } finally {
            hideLoadingModal()
        }
    }

    // Handle research key decision makers
    const handleResearchKeyDecisionMakers = async () => {
        if (!id) return

        setIsResearching(true)
        setResearchError(null)
        try {
            await researchKeyDecisionMakers.execute(Number(id))
            // Refresh company data to show new contacts
            getCompany.execute(Number(id))
        } catch (error) {
            // Error is handled in the onError callback
        }
    }

    // Handle add person to Pipedrive
    const handleAddPersonToPipedrive = async (personId: number) => {
        if (!personId) return

        showLoadingModal()
        try {
            await addPersonToPipedrive.execute(personId)
        } finally {
            hideLoadingModal()
        }
    }

    // Handle save Pipedrive contact to local database
    const handleSavePipedriveContact = async (pipedriveId: number) => {
        if (!pipedriveId || !id) return

        showLoadingModal()
        try {
            await savePipedriveContact.execute(Number(id), pipedriveId)
        } finally {
            hideLoadingModal()
        }
    }

    // Handle delete person
    const handleDeletePerson = async (personId: number, isPipedriveOnly: boolean = false) => {
        if (!personId) return

        showLoadingModal()
        try {
            if (isPipedriveOnly) {
                // If this is a Pipedrive-only contact (not in local DB), use the Pipedrive-specific endpoint
                await deletePipedrivePerson.execute(personId)
            } else {
                // Check if this ID is actually a Pipedrive ID that exists in the local database
                const localContact = company.db_contacts?.find((c: Person) => c.pipedrive_id === personId)

                if (localContact) {
                    // Use the local ID for deletion
                    await deletePerson.execute(localContact.id)
                } else {
                    // Otherwise use the regular endpoint for contacts in the local DB
                    await deletePerson.execute(personId)
                }
            }
        } finally {
            hideLoadingModal()
        }
    }

    // Handle opening the delete job confirmation modal
    const handleDeleteJob = (jobId: number | string) => {
        if (!jobId) return

        console.log('Opening delete confirmation for job ID:', jobId)
        setJobToDelete(jobId)
        setIsDeleteJobModalOpen(true)
    }

    // Handle confirming job deletion
    const handleConfirmDeleteJob = async () => {
        if (!jobToDelete) return

        console.log('Deleting job with ID:', jobToDelete)
        setIsDeleteJobModalOpen(false)

        showLoadingModal()
        try {
            await deleteJob.execute(jobToDelete)
            console.log('Job deleted successfully:', jobToDelete)
            // Reset the job to delete
            setJobToDelete(null)
        } catch (error) {
            console.error('Error deleting job:', error)
        } finally {
            hideLoadingModal()
        }
    }

    // Handle opening the delete all jobs confirmation modal
    const handleDeleteAllJobs = () => {
        if (!id) return

        console.log('Opening delete all jobs confirmation')
        setIsDeleteAllJobsModalOpen(true)
    }

    // Handle confirming deletion of all jobs
    const handleConfirmDeleteAllJobs = async () => {
        if (!id) return

        console.log('Deleting all jobs for company ID:', id)
        setIsDeleteAllJobsModalOpen(false)

        showLoadingModal()
        try {
            await deleteAllJobs.execute(Number(id))
            console.log('All jobs deleted successfully for company ID:', id)
        } catch (error) {
            console.error('Error deleting all jobs:', error)
        } finally {
            hideLoadingModal()
        }
    }

    // Handle cleaning job description
    const handleCleanJob = async (jobId: number | string) => {
        if (!jobId) return

        console.log('Cleaning job description for job ID:', jobId)
        setJobBeingCleaned(jobId)

        showLoadingModal()
        try {
            await cleanJobDescription.execute(jobId)
            console.log('Job description cleaned successfully:', jobId)
        } catch (error) {
            console.error('Error cleaning job description:', error)
        } finally {
            hideLoadingModal()
        }
    }

    // Handle add job by URL
    const handleAddJob = async () => {
        if (!id || !jobUrl.trim()) return

        showLoadingModal()
        try {
            await addJobByUrl.execute(
                Number(id),
                jobUrl.trim(),
                jobTitle.trim() || undefined,
                jobDescription.trim() || undefined
            )
        } finally {
            hideLoadingModal()
        }
    }

    // Handle opening the add job modal
    const handleOpenAddJobModal = () => {
        setJobUrl('')
        setJobTitle('')
        setJobDescription('')
        setIsAddJobModalOpen(true)
    }

    // Handle URL input change - this will be used to show a message about automatic parsing
    const handleJobUrlChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        setJobUrl(e.target.value)
        // Reset title and description if URL is cleared
        if (!e.target.value.trim()) {
            setJobTitle('')
            setJobDescription('')
        }
    }

    // Handle opening the crawl career page modal
    const handleOpenCrawlCareerPageModal = () => {
        setCareerPageUrl('')
        setIsCrawlCareerPageModalOpen(true)
    }

    // Handle crawling career page
    const handleCrawlCareerPage = async () => {
        if (!id || !careerPageUrl.trim()) return

        setIsCrawlCareerPageModalOpen(false)
        setIsCrawling(true)
        showLoadingModal()
        try {
            await crawlCareerPage.execute(
                Number(id),
                careerPageUrl.trim()
            )
        } finally {
            hideLoadingModal()
            setIsCrawling(false)
        }
    }

    // Handle research person
    const handleResearchPerson = async (personId: number) => {
        if (!personId) return

        // Set the researching person ID to show the spinner on the button
        setResearchingPersonId(personId)
        try {
            await researchPerson.execute(personId)
        } catch (error) {
            // Error handling is done in the onError callback of the hook
            console.error("Research person error:", error)
        }
    }

    // Handle opening the ignore company modal
    const handleIgnoreCompany = () => {
        if (!id) return
        setIgnoreReason('') // Reset reason
        setIsIgnoreModalOpen(true)
    }

    // Handle submitting the ignore reason
    const handleSubmitIgnoreReason = async () => {
        if (!id || !ignoreReason.trim()) return

        setIsIgnoreModalOpen(false)
        showLoadingModal()
        try {
            await ignoreCompany.execute(Number(id), ignoreReason.trim())
        } finally {
            hideLoadingModal()
        }
    }

    // Handle unignore company
    const handleUnignoreCompany = async () => {
        if (!id) return

        showLoadingModal()
        try {
            await unignoreCompany.execute(Number(id))
        } finally {
            hideLoadingModal()
        }
    }

    // Handle previewing context
    const handlePreviewContext = async () => {
        if (!id) return

        setIsLoadingPreview(true)
        setPreviewContext('')

        try {
            const response = await companyApi.previewContext(
                Number(id),
                selectedJobIds,
                selectedPersonIds
            )
            setPreviewContext(response.data.context)
            setIsPreviewingContext(true)
        } catch (error) {
            addFlashMessage('error', `Failed to preview context: ${error instanceof Error ? error.message : 'Unknown error'}`)
            console.error("Preview context error:", error)
        } finally {
            setIsLoadingPreview(false)
        }
    }

    // Handle sending a chat query
    const handleSendChatQuery = async () => {
        if (!id || !chatQuery.trim()) return

        setIsChatLoading(true)
        setChatResponse('')
        // Hide the preview context when sending a query
        setIsPreviewingContext(false)

        try {
            await sendChatQuery.execute(
                Number(id),
                chatQuery.trim(),
                selectedJobIds,
                selectedPersonIds
            )
        } catch (error) {
            // Error handling is done in the onError callback of the hook
            console.error("Chat query error:", error)
        }
    }

    // Handle toggling job selection for chat
    const handleToggleJobSelection = (jobId: number | string) => {
        const numericJobId = Number(jobId);
        setSelectedJobIds(prevIds =>
            prevIds.includes(numericJobId)
                ? prevIds.filter(id => id !== numericJobId)
                : [...prevIds, numericJobId]
        )
    }

    // Handle toggling person selection for chat
    const handleTogglePersonSelection = (personId: number) => {
        setSelectedPersonIds(prevIds =>
            prevIds.includes(personId)
                ? prevIds.filter(id => id !== personId)
                : [...prevIds, personId]
        )
    }

    // Handle starting edit mode for a profile field
    const handleStartEditing = (field: 'what_they_do' | 'current_work' | 'how_to_help') => {
        console.log('handleStartEditing called with field:', field);
        console.log('Company object:', company);

        if (!company) {
            console.error('Company object is null or undefined');
            return;
        }

        // Set the initial value for the edited field and update the editing state
        switch (field) {
            case 'what_they_do':
                console.log('Setting what_they_do edit mode');
                setEditedWhatTheyDo(company.what_they_do || '');
                setIsEditingWhatTheyDo(true);

                // Force a re-render to ensure the edit mode is applied
                setTimeout(() => {
                    console.log('Checking what_they_do edit mode after timeout:', isEditingWhatTheyDo);
                }, 0);
                break;

            case 'current_work':
                console.log('Setting current_work edit mode');
                setEditedCurrentWork(company.current_work || '');
                setIsEditingCurrentWork(true);

                // Force a re-render to ensure the edit mode is applied
                setTimeout(() => {
                    console.log('Checking current_work edit mode after timeout:', isEditingCurrentWork);
                }, 0);
                break;

            case 'how_to_help':
                console.log('Setting how_to_help edit mode');
                setEditedHowToHelp(company.how_to_help || '');
                setIsEditingHowToHelp(true);

                // Force a re-render to ensure the edit mode is applied
                setTimeout(() => {
                    console.log('Checking how_to_help edit mode after timeout:', isEditingHowToHelp);
                }, 0);
                break;

            default:
                console.error('Unknown field:', field);
                return;
        }

        // Log for debugging
        console.log(`Edit mode set for ${field}`, {
            company,
            isEditingWhatTheyDo,
            isEditingCurrentWork,
            isEditingHowToHelp
        });
    }

    // Handle saving changes to a profile field
    const handleSaveField = async (field: 'what_they_do' | 'current_work' | 'how_to_help') => {
        if (!id || !company) return

        const updatedData: any = {}
        let fieldValue = ''

        switch (field) {
            case 'what_they_do':
                updatedData.what_they_do = editedWhatTheyDo
                fieldValue = editedWhatTheyDo
                break
            case 'current_work':
                updatedData.current_work = editedCurrentWork
                fieldValue = editedCurrentWork
                break
            case 'how_to_help':
                updatedData.how_to_help = editedHowToHelp
                fieldValue = editedHowToHelp
                break
        }

        // Update local state immediately for a responsive UI
        if (localCompanyData) {
            setLocalCompanyData({
                ...localCompanyData,
                [field]: fieldValue
            })
        }

        showLoadingModal()
        try {
            await updateCompanyProfile.execute(Number(id), updatedData)
        } finally {
            hideLoadingModal()
        }
    }

    // Handle canceling edit mode
    const handleCancelEdit = (field: 'what_they_do' | 'current_work' | 'how_to_help') => {
        switch (field) {
            case 'what_they_do':
                setIsEditingWhatTheyDo(false)
                break
            case 'current_work':
                setIsEditingCurrentWork(false)
                break
            case 'how_to_help':
                setIsEditingHowToHelp(false)
                break
        }
    }

    // Handle create lead
    const handleCreateLead = async () => {
        if (!id) return

        showLoadingModal()
        try {
            await createLead.execute(Number(id))
        } finally {
            hideLoadingModal()
        }
    }

    // Handle archive lead
    const handleArchiveLead = async () => {
        if (!id) return

        showLoadingModal()
        try {
            await archiveLead.execute(Number(id))
        } finally {
            hideLoadingModal()
        }
    }

    // Handle unarchive lead
    const handleUnarchiveLead = async () => {
        if (!id) return

        showLoadingModal()
        try {
            await unarchiveLead.execute(Number(id))
        } finally {
            hideLoadingModal()
        }
    }

    // Format job data for JobsTable
    const formatJobs = (company: Company): Job[] => {
        if (!company.job_titles || company.job_titles.length === 0) {
            return []
        }

        return company.job_titles.map((job) => {
            // Check if job is an array [title, date, url, id]
            if (Array.isArray(job)) {
                // The backend returns job data as (title, date, url, id)
                // Use the actual job ID from the database (4th element in the array)
                // Make sure we have a valid ID (use -1 as fallback if undefined)
                const jobId = job[3] !== undefined ? Number(job[3]) : -1
                return {
                    id: jobId, // Use the actual job ID from the database
                    title: job[0],
                    date: job[1],
                    url: job[2] // url might be undefined
                }
            }

            // If job is just a string (legacy format)
            // This is a fallback for backward compatibility
            // In this case, we can't delete the job since we don't have a real ID
            return {
                id: -1, // Use a negative ID to indicate it's not a real database ID
                title: job as string,
                date: 'Unknown date'
            }
        })
    }

    // Handle status update
    const handleUpdateStatus = async () => {
        if (!id || !selectedStatus) return

        setIsUpdatingStatus(true)
        try {
            await companyApi.updateCompanyStatus(Number(id), selectedStatus, statusComment)
            // Refresh company data and workflow history
            getCompany.execute(Number(id))
            const response = await companyApi.getCompanyWorkflowHistory(Number(id))
            setWorkflowHistory(response.data.transitions)
            setStatusComment('')
            addFlashMessage('success', 'Company status updated successfully')
        } catch (error: any) {
            addFlashMessage('error', `Failed to update company status: ${error.message}`)
        } finally {
            setIsUpdatingStatus(false)
        }
    }

    // Loading state
    if (getCompany.isLoading) {
        return <LoadingSpinner size="lg" text="Loading company details..."/>
    }

    // Error state
    if (getCompany.error) {
        return (
            <div className="mx-auto">
                <div className="alert alert-error">
                    <svg xmlns="http://www.w3.org/2000/svg" className="stroke-current shrink-0 h-6 w-6" fill="none"
                         viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2"
                              d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                    </svg>
                    <span>Error loading company: {getCompany.error.message}</span>
                </div>
            </div>
        )
    }

    // No data state
    if (!getCompany.data) {
        return (
            <div className="mx-auto">
                <div className="alert alert-warning">
                    <svg xmlns="http://www.w3.org/2000/svg" className="stroke-current shrink-0 h-6 w-6" fill="none"
                         viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2"
                              d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"/>
                    </svg>
                    <span>Company not found</span>
                </div>
            </div>
        )
    }

    // Use localCompanyData if available, otherwise use getCompany.data
    const company = localCompanyData || getCompany.data
    console.log('Company data:', company)
    const jobs = formatJobs(company)
    console.log('Formatted jobs:', jobs)

    // Prepare tabs for the company detail page
    const tabs: TabItem[] = [
        {
            id: 'overview',
            label: 'Overview',
            content: (
                <CompanyCard
                    id={company.id}
                    name={company.name}
                    industry={company.industry}
                    description={company.description}
                    icpRating={company.icp_rating !== undefined ? company.icp_rating : undefined}
                    icpDescription={company.icp_description}
                    jobTitles={company.job_titles || []}
                    ignored={company.ignored}
                    ignoreReason={company.ignore_reason}
                    created_at={company.created_at}

                    clickable={false}
                    onReanalyze={handleReanalyze}
                    onReanalyzeSkipDescription={handleReanalyzeSkipDescription}
                    onResearchDescription={handleResearchDescription}
                    onAddJobByUrl={handleOpenAddJobModal}
                    onSearchJobs={handleSearchJobs}
                    onIgnore={handleIgnoreCompany}
                    onUnignore={handleUnignoreCompany}
                />
            )
        },
        {
            id: 'profile',
            label: 'Company Profile',
            content: (
                <Card>
                    <h2 className="text-2xl font-bold mb-6">Company Profile</h2>

                    {company.what_they_do || company.current_work || company.how_to_help || isEditingWhatTheyDo || isEditingCurrentWork || isEditingHowToHelp ? (
                        <div>
                            {/* What They Do section */}
                            <div className="mb-6">
                                <div className="flex justify-between items-center mb-2">
                                    <h3 className="text-xl font-semibold">What They Do</h3>
                                    {!isEditingWhatTheyDo && (
                                        <button
                                            className="btn btn-sm btn-outline"
                                            onClick={() => handleStartEditing('what_they_do')}
                                        >
                                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                                                 strokeWidth={1.5} stroke="currentColor" className="w-4 h-4 mr-1">
                                                <path strokeLinecap="round" strokeLinejoin="round"
                                                      d="m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L10.582 16.07a4.5 4.5 0 0 1-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 0 1 1.13-1.897l8.932-8.931Zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0 1 15.75 21H5.25A2.25 2.25 0 0 1 3 18.75V8.25A2.25 2.25 0 0 1 5.25 6H10"/>
                                            </svg>
                                            Edit
                                        </button>
                                    )}
                                </div>

                                {isEditingWhatTheyDo ? (
                                    <div>
                                        <textarea
                                            className="textarea textarea-bordered w-full h-48 mb-2"
                                            value={editedWhatTheyDo}
                                            onChange={(e) => setEditedWhatTheyDo(e.target.value)}
                                            placeholder="Describe what this company does..."
                                        />
                                        <div className="flex justify-end gap-2">
                                            <button
                                                className="btn btn-sm btn-outline"
                                                onClick={() => handleCancelEdit('what_they_do')}
                                            >
                                                Cancel
                                            </button>
                                            <button
                                                className="btn btn-sm btn-primary"
                                                onClick={() => handleSaveField('what_they_do')}
                                                disabled={updateCompanyProfile.isLoading}
                                            >
                                                {updateCompanyProfile.isLoading ? (
                                                    <>
                                                        <span
                                                            className="loading loading-spinner loading-xs mr-1"></span>
                                                        Saving...
                                                    </>
                                                ) : 'Save'}
                                            </button>
                                        </div>
                                    </div>
                                ) : (
                                    company.what_they_do ? (
                                        <div className="prose max-w-none bg-base-200 p-4 rounded-box"
                                             dangerouslySetInnerHTML={{__html: markdownToHtml(company.what_they_do)}}/>
                                    ) : (
                                        <div className="bg-base-200 p-4 rounded-box text-base-content/70 italic">
                                            No information available. Click 'Edit' to add details.
                                        </div>
                                    )
                                )}
                            </div>

                            {/* Current Work section */}
                            <div className="mb-6">
                                <div className="flex justify-between items-center mb-2">
                                    <h3 className="text-xl font-semibold">Current Work & Job Openings</h3>
                                    {!isEditingCurrentWork && (
                                        <button
                                            className="btn btn-sm btn-outline"
                                            onClick={() => handleStartEditing('current_work')}
                                        >
                                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                                                 strokeWidth={1.5} stroke="currentColor" className="w-4 h-4 mr-1">
                                                <path strokeLinecap="round" strokeLinejoin="round"
                                                      d="m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L10.582 16.07a4.5 4.5 0 0 1-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 0 1 1.13-1.897l8.932-8.931Zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0 1 15.75 21H5.25A2.25 2.25 0 0 1 3 18.75V8.25A2.25 2.25 0 0 1 5.25 6H10"/>
                                            </svg>
                                            Edit
                                        </button>
                                    )}
                                </div>

                                {isEditingCurrentWork ? (
                                    <div>
                                        <textarea
                                            className="textarea textarea-bordered w-full h-48 mb-2"
                                            value={editedCurrentWork}
                                            onChange={(e) => setEditedCurrentWork(e.target.value)}
                                            placeholder="Describe what the company is currently working on..."
                                        />
                                        <div className="flex justify-end gap-2">
                                            <button
                                                className="btn btn-sm btn-outline"
                                                onClick={() => handleCancelEdit('current_work')}
                                            >
                                                Cancel
                                            </button>
                                            <button
                                                className="btn btn-sm btn-primary"
                                                onClick={() => handleSaveField('current_work')}
                                                disabled={updateCompanyProfile.isLoading}
                                            >
                                                {updateCompanyProfile.isLoading ? (
                                                    <>
                                                        <span
                                                            className="loading loading-spinner loading-xs mr-1"></span>
                                                        Saving...
                                                    </>
                                                ) : 'Save'}
                                            </button>
                                        </div>
                                    </div>
                                ) : (
                                    company.current_work ? (
                                        <div className="prose max-w-none bg-base-200 p-4 rounded-box"
                                             dangerouslySetInnerHTML={{__html: markdownToHtml(company.current_work)}}/>
                                    ) : (
                                        <div className="bg-base-200 p-4 rounded-box text-base-content/70 italic">
                                            No information available. Click 'Edit' to add details.
                                        </div>
                                    )
                                )}
                            </div>

                            {/* How to Help section */}
                            <div className="mb-6">
                                <div className="flex justify-between items-center mb-2">
                                    <h3 className="text-xl font-semibold">How to Help</h3>
                                    {!isEditingHowToHelp && (
                                        <button
                                            className="btn btn-sm btn-outline"
                                            onClick={() => handleStartEditing('how_to_help')}
                                        >
                                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                                                 strokeWidth={1.5} stroke="currentColor" className="w-4 h-4 mr-1">
                                                <path strokeLinecap="round" strokeLinejoin="round"
                                                      d="m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L10.582 16.07a4.5 4.5 0 0 1-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 0 1 1.13-1.897l8.932-8.931Zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0 1 15.75 21H5.25A2.25 2.25 0 0 1 3 18.75V8.25A2.25 2.25 0 0 1 5.25 6H10"/>
                                            </svg>
                                            Edit
                                        </button>
                                    )}
                                </div>

                                {isEditingHowToHelp ? (
                                    <div>
                                        <textarea
                                            className="textarea textarea-bordered w-full h-48 mb-2"
                                            value={editedHowToHelp}
                                            onChange={(e) => setEditedHowToHelp(e.target.value)}
                                            placeholder="Describe how to help this company..."
                                        />
                                        <div className="flex justify-end gap-2">
                                            <button
                                                className="btn btn-sm btn-outline"
                                                onClick={() => handleCancelEdit('how_to_help')}
                                            >
                                                Cancel
                                            </button>
                                            <button
                                                className="btn btn-sm btn-primary"
                                                onClick={() => handleSaveField('how_to_help')}
                                                disabled={updateCompanyProfile.isLoading}
                                            >
                                                {updateCompanyProfile.isLoading ? (
                                                    <>
                                                        <span
                                                            className="loading loading-spinner loading-xs mr-1"></span>
                                                        Saving...
                                                    </>
                                                ) : 'Save'}
                                            </button>
                                        </div>
                                    </div>
                                ) : (
                                    company.how_to_help ? (
                                        <div className="prose max-w-none bg-base-200 p-4 rounded-box"
                                             dangerouslySetInnerHTML={{__html: markdownToHtml(company.how_to_help)}}/>
                                    ) : (
                                        <div className="bg-base-200 p-4 rounded-box text-base-content/70 italic">
                                            No information available. Click 'Edit' to add details.
                                        </div>
                                    )
                                )}
                            </div>
                        </div>
                    ) : (
                        <>
                            <div className="alert alert-info">
                                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                                     className="stroke-current shrink-0 w-6 h-6">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2"
                                          d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                <span>No company profile information available. Click the 'Edit' buttons to add details for each section.</span>
                            </div>

                            <div className="mt-6 space-y-6">
                                {/* What They Do section */}
                                <div className="mb-6">
                                    <div className="flex justify-between items-center mb-2">
                                        <h3 className="text-xl font-semibold">What They Do</h3>
                                        <button
                                            className="btn btn-sm btn-outline"
                                            onClick={() => {
                                                console.log('Add Details button clicked for what_they_do');
                                                handleStartEditing('what_they_do');
                                            }}
                                        >
                                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                                                 strokeWidth={1.5} stroke="currentColor" className="w-4 h-4 mr-1">
                                                <path strokeLinecap="round" strokeLinejoin="round"
                                                      d="m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L10.582 16.07a4.5 4.5 0 0 1-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 0 1 1.13-1.897l8.932-8.931Zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0 1 15.75 21H5.25A2.25 2.25 0 0 1 3 18.75V8.25A2.25 2.25 0 0 1 5.25 6H10"/>
                                            </svg>
                                            Add Details
                                        </button>
                                    </div>
                                    <div className="bg-base-200 p-4 rounded-box text-base-content/70 italic">
                                        No information available. Click 'Add Details' to add information.
                                    </div>
                                </div>

                                {/* Current Work section */}
                                <div className="mb-6">
                                    <div className="flex justify-between items-center mb-2">
                                        <h3 className="text-xl font-semibold">Current Work & Job Openings</h3>
                                        <button
                                            className="btn btn-sm btn-outline"
                                            onClick={() => {
                                                console.log('Add Details button clicked for current_work');
                                                handleStartEditing('current_work');
                                            }}
                                        >
                                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                                                 strokeWidth={1.5} stroke="currentColor" className="w-4 h-4 mr-1">
                                                <path strokeLinecap="round" strokeLinejoin="round"
                                                      d="m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L10.582 16.07a4.5 4.5 0 0 1-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 0 1 1.13-1.897l8.932-8.931Zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0 1 15.75 21H5.25A2.25 2.25 0 0 1 3 18.75V8.25A2.25 2.25 0 0 1 5.25 6H10"/>
                                            </svg>
                                            Add Details
                                        </button>
                                    </div>
                                    <div className="bg-base-200 p-4 rounded-box text-base-content/70 italic">
                                        No information available. Click 'Add Details' to add information.
                                    </div>
                                </div>

                                {/* How to Help section */}
                                <div className="mb-6">
                                    <div className="flex justify-between items-center mb-2">
                                        <h3 className="text-xl font-semibold">How to Help</h3>
                                        <button
                                            className="btn btn-sm btn-outline"
                                            onClick={() => {
                                                console.log('Add Details button clicked for how_to_help');
                                                handleStartEditing('how_to_help');
                                            }}
                                        >
                                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                                                 strokeWidth={1.5} stroke="currentColor" className="w-4 h-4 mr-1">
                                                <path strokeLinecap="round" strokeLinejoin="round"
                                                      d="m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L10.582 16.07a4.5 4.5 0 0 1-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 0 1 1.13-1.897l8.932-8.931Zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0 1 15.75 21H5.25A2.25 2.25 0 0 1 3 18.75V8.25A2.25 2.25 0 0 1 5.25 6H10"/>
                                            </svg>
                                            Add Details
                                        </button>
                                    </div>
                                    <div className="bg-base-200 p-4 rounded-box text-base-content/70 italic">
                                        No information available. Click 'Add Details' to add information.
                                    </div>
                                </div>
                            </div>
                        </>
                    )}

                    {/* No need for an Edit Profile button anymore since we have inline editing */}
                </Card>
            )
        },
        {
            id: 'analysis',
            label: 'ICP',
            content: (
                <Card>
                    <div className="mb-6">
                        <h2 className="text-2xl font-bold mb-4">ICP Rating Analysis</h2>

                        <div className="flex items-center gap-4 mb-6">
                            <div className="radial-progress text-primary"
                                 style={{'--value': company.icp_rating ? company.icp_rating : 0} as any}>
                                {company.icp_rating ? company.icp_rating : 'N/A'}
                            </div>

                            <div>
                                <h3 className="text-xl font-semibold mb-2">Overall
                                    Rating: {company.icp_rating ? company.icp_rating : 'Not Rated'}/100</h3>
                                <p className="text-base-content/70">
                                    This rating indicates how well this company matches your Ideal Customer Profile.
                                </p>
                            </div>
                        </div>

                        {company.icp_description && (
                            <div className="prose max-w-none">
                                <h3 className="text-xl font-semibold mb-2">Analysis Details</h3>
                                <div dangerouslySetInnerHTML={{__html: markdownToHtml(company.icp_description)}}/>
                            </div>
                        )}
                    </div>

                    <div className="flex justify-end mt-6">
                        <button
                            className="btn btn-primary"
                            onClick={handleReanalyze}
                            disabled={reanalyzeCompany.isLoading}
                        >
                            {reanalyzeCompany.isLoading ? (
                                <>
                                    <span className="loading loading-spinner loading-xs mr-2"></span>
                                    Reanalyzing...
                                </>
                            ) : (
                                '🔄 Reanalyze Company'
                            )}
                        </button>
                    </div>
                </Card>
            )
        },
        {
            id: 'workflow',
            label: 'Workflow',
            content: (
                <Card>
                    <div className="mb-8">
                        <h3 className="text-xl font-semibold mb-4">Company Status</h3>
                        <div className="flex gap-4 items-end">
                            <div className="form-control flex-1">
                                <label className="label">
                                    <span className="label-text">Status</span>
                                </label>
                                <select
                                    className="select select-bordered w-full"
                                    value={selectedStatus}
                                    onChange={(e) => setSelectedStatus(e.target.value)}
                                >
                                    <option value="">Select a status</option>
                                    {statuses.map(status => (
                                        <option key={status.id} value={status.name}>
                                            {status.name}
                                        </option>
                                    ))}
                                </select>
                            </div>
                            <div className="form-control flex-1">
                                <label className="label">
                                    <span className="label-text">Comment (optional)</span>
                                </label>
                                <input
                                    type="text"
                                    className="input input-bordered w-full"
                                    value={statusComment}
                                    onChange={(e) => setStatusComment(e.target.value)}
                                    placeholder="Add a comment about this status change"
                                />
                            </div>
                            <button
                                className="btn btn-primary"
                                onClick={handleUpdateStatus}
                                disabled={!selectedStatus || isUpdatingStatus}
                            >
                                {isUpdatingStatus ? (
                                    <>
                                        <span className="loading loading-spinner loading-xs mr-2"></span>
                                        Updating...
                                    </>
                                ) : (
                                    'Update Status'
                                )}
                            </button>
                        </div>
                    </div>

                    <div>
                        <h3 className="text-xl font-semibold mb-4">Workflow History</h3>
                        <div className="overflow-x-auto">
                            <table className="table">
                                <thead>
                                    <tr>
                                        <th>From</th>
                                        <th>To</th>
                                        <th>Comment</th>
                                        <th>Changed By</th>
                                        <th>Date</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {workflowHistory.map(transition => (
                                        <tr key={transition.id}>
                                            <td>{transition.from_status || '-'}</td>
                                            <td>{transition.to_status}</td>
                                            <td>{transition.comment || '-'}</td>
                                            <td>{transition.created_by}</td>
                                            <td>{new Date(transition.created_at).toLocaleString()}</td>
                                        </tr>
                                    ))}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </Card>
            )
        }
    ]

    // Add Jobs tab
    tabs.push({
        id: 'jobs',
        label: `Jobs (${jobs.length})`,
        content: (
            <Card>
                <JobsTable
                    jobs={jobs}
                    title={`Job Openings (${jobs.length})`}
                    description="Current job openings at this company"
                    isLoading={searchJobs.isLoading}
                    emptyMessage="No job openings found. Click 'Search for more jobs' to find current openings."
                    onDeleteJob={handleDeleteJob}
                    onCleanJob={handleCleanJob}
                    cleaningJobId={jobBeingCleaned}
                />
                <div className="mt-4 flex justify-between gap-2">
                    <div>
                        {jobs.length > 0 && (
                            <button
                                className="btn btn-error"
                                onClick={handleDeleteAllJobs}
                            >
                                🗑️ Delete All
                            </button>
                        )}
                    </div>
                    <div className="flex gap-2">
                        <button
                            className="btn btn-secondary"
                            onClick={handleOpenAddJobModal}
                        >
                            ➕ Add Job by URL
                        </button>
                        <button
                            className="btn btn-secondary"
                            onClick={handleOpenCrawlCareerPageModal}
                        >
                            🕸️ Crawl Career Page
                        </button>
                        <button
                            className="btn btn-primary"
                            onClick={handleSearchJobs}
                            disabled={searchJobs.isLoading}
                        >
                            {searchJobs.isLoading ? (
                                <>
                                    <span className="loading loading-spinner loading-xs mr-2"></span>
                                    Searching...
                                </>
                            ) : (
                                '🔍 Search for Jobs'
                            )}
                        </button>
                    </div>
                </div>
            </Card>
        )
    })

    // Add People tab for all contacts and people-related actions
    tabs.push({
        id: 'people',
        label: `People (${company.db_contacts ? company.db_contacts.length : 0})`,
        content: (
            <Card>
                <h2 className="text-2xl font-bold mb-4">People</h2>

                {/* Database Contacts */}
                {company.db_contacts && company.db_contacts.length > 0 ? (
                    <div className="mb-8">
                        <h3 className="text-xl font-semibold mb-2">Database Contacts</h3>
                        <p className="mb-4">Contacts associated with this company in the database</p>
                        <ContactsTable
                            contacts={company.db_contacts as Person[]}
                            title=""
                            description=""
                            showResearchButton={true}
                            onResearchPerson={handleResearchPerson}
                            researchingPersonId={researchingPersonId}
                            showAddToPipedriveButton={company.pipedrive_enabled}
                            onAddToPipedrive={handleAddPersonToPipedrive}
                            showDeleteButton={true}
                            onDelete={handleDeletePerson}
                        />
                    </div>
                ) : (
                    <div className="alert alert-info mb-8">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                             className="stroke-current shrink-0 w-6 h-6">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2"
                                  d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        <span>No contacts found in the database for this company.</span>
                    </div>
                )}

                {/* Key Decision Makers */}
                <div className="mb-8">
                    <h3 className="text-xl font-semibold mb-2">Key Decision Makers</h3>
                    <p className="mb-4">Research and identify key decision makers at this company</p>

                    <div className="mb-6">
                        <p className="mb-4">
                            Use AI to research key decision makers at this company.
                        </p>
                        <button
                            className="btn btn-primary"
                            onClick={handleResearchKeyDecisionMakers}
                            disabled={isResearching}
                        >
                            {isResearching ? (
                                <>
                                    <span className="loading loading-spinner loading-xs mr-2"></span>
                                    Researching...
                                </>
                            ) : (
                                '🔍 Research Key Decision Makers'
                            )}
                        </button>
                        {researchError && (
                            <div className="alert alert-error mt-4">
                                <svg xmlns="http://www.w3.org/2000/svg" className="stroke-current shrink-0 h-6 w-6"
                                     fill="none" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2"
                                          d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                </svg>
                                <span>{researchError}</span>
                            </div>
                        )}
                    </div>
                </div>
            </Card>
        )
    })

    // Add Pipedrive tab with all Pipedrive-related actions and data
    if (company.pipedrive_enabled) {
        tabs.push({
            id: 'pipedrive',
            label: 'Pipedrive',
            content: (
                <Card>
                    <h2 className="text-2xl font-bold mb-4">Pipedrive Integration</h2>

                    {!company.pipedrive_enabled ? (
                        <div className="alert alert-warning mb-4">
                            <svg xmlns="http://www.w3.org/2000/svg" className="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                            </svg>
                            <div>
                                <h3 className="font-bold">Pipedrive Integration Disabled</h3>
                                <div className="text-sm">
                                    Pipedrive integration is currently disabled. Please set up your Pipedrive API key in the settings to enable this feature.
                                </div>
                            </div>
                        </div>
                    ) : company.pipedrive_org_id ? (
                    <div className="mb-6">
                        <div className="alert alert-success mb-4">
                            <svg xmlns="http://www.w3.org/2000/svg" className="stroke-current shrink-0 h-6 w-6"
                                 fill="none" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2"
                                      d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                            </svg>
                            <div>
                                <h3 className="font-bold">Connected to Pipedrive</h3>
                                <div className="text-sm">
                                    This company is synced with Pipedrive
                                    as {company.pipedrive_org_name} (ID: {company.pipedrive_org_id})
                                </div>
                            </div>
                        </div>

                        {/* Pipedrive Lead Section */}
                        <div className="mb-8">
                            <h3 className="text-xl font-semibold mb-2">Pipedrive Lead</h3>
                            <div className="alert mb-4 p-4 rounded-box">
                                {hasLead ? (
                                    <div className="flex items-center">
                                        <span
                                            className={`badge mr-2 ${isLeadArchived ? 'badge-warning' : 'badge-success'}`}>
                                            {isLeadArchived ? '!' : '✓'}
                                        </span>
                                        <span>
                                            This company has a lead in Pipedrive {leadId && `(ID: ${leadId})`}
                                            {isLeadArchived && ' (Archived)'}
                                        </span>
                                    </div>
                                ) : (
                                    <div className="flex items-center">
                                        <span className="badge badge-warning mr-2">!</span>
                                        <span>This company does not have a lead in Pipedrive</span>
                                    </div>
                                )}
                            </div>
                            <div className="flex gap-2 mb-6">
                                {hasLead ? (
                                    isLeadArchived ? (
                                        <button
                                            className="btn btn-outline btn-success"
                                            onClick={handleUnarchiveLead}
                                            disabled={unarchiveLead.isLoading}
                                            title="Unarchive this lead in Pipedrive"
                                            type="button"
                                        >
                                            {unarchiveLead.isLoading ? (
                                                <>
                                                    <span className="loading loading-spinner loading-xs mr-2"></span>
                                                    Unarchiving...
                                                </>
                                            ) : (
                                                <><span className="mr-2">🔓</span> Unarchive Lead</>
                                            )}
                                        </button>
                                    ) : (
                                        <button
                                            className="btn btn-outline btn-error"
                                            onClick={handleArchiveLead}
                                            disabled={archiveLead.isLoading}
                                            title="Archive this lead in Pipedrive"
                                            type="button"
                                        >
                                            {archiveLead.isLoading ? (
                                                <>
                                                    <span className="loading loading-spinner loading-xs mr-2"></span>
                                                    Archiving...
                                                </>
                                            ) : (
                                                <><span className="mr-2">📁</span> Archive Lead</>
                                            )}
                                        </button>
                                    )
                                ) : (
                                    <button
                                        className="btn btn-outline btn-primary"
                                        onClick={handleCreateLead}
                                        disabled={createLead.isLoading}
                                        title="Create a lead in Pipedrive"
                                        type="button"
                                    >
                                        {createLead.isLoading ? (
                                            <>
                                                <span className="loading loading-spinner loading-xs mr-2"></span>
                                                Creating...
                                            </>
                                        ) : (
                                            <><span className="mr-2">➕</span> Create Lead</>
                                        )}
                                    </button>
                                )}
                            </div>
                        </div>

                        <div className="flex flex-wrap gap-4 mb-6">
                            <a
                                href={company.pipedrive_url}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="btn btn-primary"
                            >
                                🔗 Open in Pipedrive
                            </a>

                            <button
                                className="btn btn-outline"
                                onClick={handleSyncPipedrive}
                                disabled={syncPipedrive.isLoading}
                            >
                                {syncPipedrive.isLoading ? (
                                    <>
                                        <span className="loading loading-spinner loading-xs mr-2"></span>
                                        Syncing...
                                    </>
                                ) : (
                                    '🔄 Sync to Pipedrive'
                                )}
                            </button>

                            <button
                                className="btn btn-outline btn-info"
                                onClick={handleSyncPipedriveContacts}
                                disabled={syncPipedriveContacts.isLoading}
                            >
                                {syncPipedriveContacts.isLoading ? (
                                    <>
                                        <span className="loading loading-spinner loading-xs mr-2"></span>
                                        Syncing Contacts...
                                    </>
                                ) : (
                                    '👥 Sync Contacts to Database'
                                )}
                            </button>

                            <button
                                className="btn btn-outline btn-error"
                                onClick={handleDisconnectPipedrive}
                                disabled={disconnectPipedrive.isLoading}
                            >
                                {disconnectPipedrive.isLoading ? (
                                    <>
                                        <span className="loading loading-spinner loading-xs mr-2"></span>
                                        Disconnecting...
                                    </>
                                ) : (
                                    '❌ Disconnect from Pipedrive'
                                )}
                            </button>
                        </div>

                        {/* Pipedrive Contacts */}
                        {company.pipedrive_contacts && company.pipedrive_contacts.length > 0 ? (
                            <div className="mb-8">
                                <h3 className="text-xl font-semibold mb-2">Pipedrive Contacts</h3>
                                <p className="mb-4">Contacts associated with this company in Pipedrive</p>

                                <ContactsTable
                                    contacts={company.pipedrive_contacts as Person[]}
                                    title=""
                                    description=""
                                    showSaveToLocalButton={true}
                                    onSaveToLocal={handleSavePipedriveContact}
                                    showDeleteButton={true}
                                    onDelete={(personId) => handleDeletePerson(personId, true)} // Pass true for isPipedriveOnly
                                    dbContactIds={company.db_contacts?.filter((contact: Person) => contact.pipedrive_id).map((contact: Person) => contact.pipedrive_id) || []}
                                    isPipedriveList={true}
                                />
                            </div>
                        ) : (
                            <div className="alert alert-info mb-8">
                                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                                     className="stroke-current shrink-0 w-6 h-6">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2"
                                          d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                <span>No contacts found in Pipedrive for this company.</span>
                            </div>
                        )}

                        {/* Local Contacts with Pipedrive ID */}
                        {company.db_contacts && company.db_contacts.filter((contact: Person) => contact.pipedrive_id).length > 0 ? (
                            <div className="mb-8">
                                <h3 className="text-xl font-semibold mb-2">Local Contacts with Pipedrive ID</h3>
                                <p className="mb-4">Contacts in your local database that are linked to Pipedrive</p>

                                <ContactsTable
                                    contacts={company.db_contacts.filter((contact: Person) => contact.pipedrive_id) as Person[]}
                                    title=""
                                    description=""
                                    showDeleteButton={true}
                                    onDelete={(personId) => handleDeletePerson(personId, false)} // Pass false for isPipedriveOnly
                                    isPipedriveList={false}
                                />
                            </div>
                        ) : null}
                    </div>
                ) : (
                    <div className="alert alert-info mb-4">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                             className="stroke-current shrink-0 w-6 h-6">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2"
                                  d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        <div>
                            <h3 className="font-bold">Not Connected to Pipedrive</h3>
                            <div className="text-sm">
                                This company is not yet connected to Pipedrive. Click the button below to connect it.
                            </div>
                        </div>
                    </div>
                )}

                {company.pipedrive_enabled && !company.pipedrive_org_id && (
                    <div className="flex justify-center">
                        <button
                            className="btn btn-primary"
                            onClick={handleSyncPipedrive}
                            disabled={syncPipedrive.isLoading}
                        >
                            {syncPipedrive.isLoading ? (
                                <>
                                    <span className="loading loading-spinner loading-xs mr-2"></span>
                                    Connecting...
                                </>
                            ) : (
                                '🔗 Connect to Pipedrive'
                            )}
                        </button>
                    </div>
                )}
            </Card>
        )
    })
    }

    // Add Chat tab
    tabs.push({
        id: 'chat',
        label: 'Chat',
        content: (
            <Card className="shadow-xl border border-base-300">
                <div className="bg-gradient-to-r from-primary/10 to-secondary/10 p-6 rounded-t-box border-b border-base-300">
                    <div className="flex items-center gap-3 mb-2">
                        <div className="bg-primary/20 p-2 rounded-full">
                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-6 h-6 text-primary">
                                <path strokeLinecap="round" strokeLinejoin="round" d="M7.5 8.25h9m-9 3H12m-9.75 1.51c0 1.6 1.123 2.994 2.707 3.227 1.129.166 2.27.293 3.423.379.************.865.501L12 21l2.755-4.133a1.14 1.14 0 0 1 .865-.501 48.172 48.172 0 0 0 3.423-.379c1.584-.233 2.707-1.626 2.707-3.228V6.741c0-1.602-1.123-2.995-2.707-3.228A48.394 48.394 0 0 0 12 3c-2.392 0-4.744.175-7.043.513C3.373 3.746 2.25 5.14 2.25 6.741v6.018Z" />
                            </svg>
                        </div>
                        <h2 className="text-2xl font-bold">Chat with AI</h2>
                    </div>
                </div>

                <div className="p-6">
                    {/* Wrap job and person selection in a flex container */}
                    <div className="flex flex-col md:flex-row gap-6 mb-8">
                        {/* Job selection - now in left column */}
                        <div className="flex-1">
                            <div className="flex items-center gap-2 mb-3">
                                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-5 h-5 text-primary">
                                    <path strokeLinecap="round" strokeLinejoin="round" d="M20.25 14.15v4.25c0 1.094-.787 2.036-1.872 2.18-2.087.277-4.216.42-6.378.42s-4.291-.143-6.378-.42c-1.085-.144-1.872-1.086-1.872-2.18v-4.25m16.5 0a2.18 2.18 0 0 0 .75-1.661V8.706c0-1.081-.768-2.015-1.837-2.175a48.114 48.114 0 0 0-3.413-.387m4.5 8.006c-.194.165-.42.295-.673.38A23.978 23.978 0 0 1 12 15.75c-2.648 0-5.195-.429-7.577-1.22a2.016 2.016 0 0 1-.673-.38m0 0A2.18 2.18 0 0 1 3 12.489V8.706c0-1.081.768-2.015 1.837-2.175a48.111 48.111 0 0 1 3.413-.387m7.5 0V5.25A2.25 2.25 0 0 0 13.5 3h-3a2.25 2.25 0 0 0-2.25 2.25v.894m7.5 0a48.667 48.667 0 0 0-7.5 0M12 12.75h.008v.008H12v-.008Z" />
                                </svg>
                                <h3 className="text-xl font-semibold">Select Jobs to Include</h3>
                            </div>
                            {jobs.length > 0 ? (
                                <div className="bg-base-200 p-5 rounded-box h-full border border-base-300 shadow-md hover:shadow-lg transition-shadow duration-300">
                                    <div className="max-h-60 overflow-y-auto pr-2">
                                        {jobs.map(job => (
                                            <div key={job.id} className="form-control mb-2 last:mb-0">
                                                <label className="label cursor-pointer justify-start hover:bg-base-300/50 rounded-lg px-2 py-1 transition-colors duration-200">
                                                    <input
                                                        type="checkbox"
                                                        className="checkbox checkbox-primary mr-3"
                                                        checked={selectedJobIds.includes(Number(job.id))}
                                                        onChange={() => handleToggleJobSelection(job.id)}
                                                    />
                                                    <span className="label-text font-medium">{job.title}</span>
                                                </label>
                                            </div>
                                        ))}
                                    </div>
                                </div>
                            ) : (
                                <div className="alert alert-info h-full shadow-md border border-info/30">
                                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                                         className="stroke-current shrink-0 w-6 h-6">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2"
                                              d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    <span>No jobs found for this company.</span>
                                </div>
                            )}
                        </div>

                        {/* Person selection - now in right column */}
                        <div className="flex-1">
                            <div className="flex items-center gap-2 mb-3">
                                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-5 h-5 text-primary">
                                    <path strokeLinecap="round" strokeLinejoin="round" d="M15 19.128a9.38 9.38 0 0 0 2.625.372 9.337 9.337 0 0 0 4.121-.952 4.125 4.125 0 0 0-7.533-2.493M15 19.128v-.003c0-1.113-.285-2.16-.786-3.07M15 19.128v.106A12.318 12.318 0 0 1 8.624 21c-2.331 0-4.512-.645-6.374-1.766l-.001-.109a6.375 6.375 0 0 1 11.964-3.07M12 6.375a3.375 3.375 0 1 1-6.75 0 3.375 3.375 0 0 1 6.75 0Zm8.25 2.25a2.625 2.625 0 1 1-5.25 0 2.625 2.625 0 0 1 5.25 0Z" />
                                </svg>
                                <h3 className="text-xl font-semibold">Select People to Include</h3>
                            </div>
                            {company.db_contacts && company.db_contacts.length > 0 ? (
                                <div className="bg-base-200 p-5 rounded-box h-full border border-base-300 shadow-md hover:shadow-lg transition-shadow duration-300">
                                    <div className="max-h-60 overflow-y-auto pr-2">
                                        {company.db_contacts.map((person: Person) => (
                                            <div key={person.id} className="form-control mb-2 last:mb-0">
                                                <label className="label cursor-pointer justify-start hover:bg-base-300/50 rounded-lg px-2 py-1 transition-colors duration-200">
                                                    <input
                                                        type="checkbox"
                                                        className="checkbox checkbox-primary mr-3"
                                                        checked={selectedPersonIds.includes(person.id)}
                                                        onChange={() => handleTogglePersonSelection(person.id)}
                                                    />
                                                    <div>
                                                        <span className="label-text font-medium">{person.name}</span>
                                                        {person.position && <span className="label-text block text-xs opacity-70">{person.position}</span>}
                                                    </div>
                                                </label>
                                            </div>
                                        ))}
                                    </div>
                                </div>
                            ) : (
                                <div className="alert alert-info h-full shadow-md border border-info/30">
                                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                                         className="stroke-current shrink-0 w-6 h-6">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2"
                                              d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    <span>No people found for this company.</span>
                                </div>
                            )}
                        </div>
                    </div>

                    <div className="mb-8 border border-base-300 rounded-box p-6 shadow-md bg-base-100">
                        <div className="flex items-center gap-2 mb-3">
                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-5 h-5 text-primary">
                                <path strokeLinecap="round" strokeLinejoin="round" d="M9.879 7.519c1.171-1.025 3.071-1.025 4.242 0 1.172 1.025 1.172 2.687 0 3.712-.203.179-.43.326-.67.442-.745.361-1.45.999-1.45 1.827v.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Zm-9 5.25h.008v.008H12v-.008Z" />
                            </svg>
                            <h3 className="text-xl font-semibold">Ask a Question</h3>
                        </div>
                        <div className="form-control">
                            <textarea
                                className="textarea textarea-bordered h-28 mb-3 shadow-inner focus:shadow-md transition-shadow duration-300 text-base"
                                placeholder="Enter your question here..."
                                value={chatQuery}
                                onChange={(e) => setChatQuery(e.target.value)}
                            ></textarea>
                            <div className="flex justify-end gap-2">
                                <button
                                    className="btn btn-outline shadow-md hover:shadow-lg transition-shadow duration-300"
                                    onClick={handlePreviewContext}
                                    disabled={isLoadingPreview}
                                >
                                    {isLoadingPreview ? (
                                        <>
                                            <span className="loading loading-spinner loading-xs mr-2"></span>
                                            Loading...
                                        </>
                                    ) : (
                                        <>
                                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-5 h-5 mr-1">
                                                <path strokeLinecap="round" strokeLinejoin="round" d="M2.036 12.322a1.012 1.012 0 010-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178z" />
                                                <path strokeLinecap="round" strokeLinejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                            </svg>
                                            Preview Context
                                        </>
                                    )}
                                </button>
                                <button
                                    className="btn btn-primary shadow-md hover:shadow-lg transition-shadow duration-300"
                                    onClick={handleSendChatQuery}
                                    disabled={isChatLoading || !chatQuery.trim()}
                                >
                                    {isChatLoading ? (
                                        <>
                                            <span className="loading loading-spinner loading-xs mr-2"></span>
                                            Thinking...
                                        </>
                                    ) : (
                                        <>
                                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-5 h-5 mr-1">
                                                <path strokeLinecap="round" strokeLinejoin="round" d="M4.5 12h15m0 0-6.75-6.75M19.5 12l-6.75 6.75" />
                                            </svg>
                                            Ask AI
                                        </>
                                    )}
                                </button>
                            </div>
                        </div>
                    </div>

                    {isPreviewingContext && previewContext && (
                        <div className="mb-6 border border-base-300 rounded-box shadow-lg bg-base-100 overflow-hidden">
                            <div className="bg-secondary/10 p-4 border-b border-base-300 flex items-center justify-between">
                                <div className="flex items-center gap-2">
                                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-5 h-5 text-secondary">
                                        <path strokeLinecap="round" strokeLinejoin="round" d="M2.036 12.322a1.012 1.012 0 010-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178z" />
                                        <path strokeLinecap="round" strokeLinejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                    </svg>
                                    <h3 className="text-xl font-semibold">Context Preview</h3>
                                </div>
                                <button 
                                    className="btn btn-sm btn-outline"
                                    onClick={() => setIsPreviewingContext(false)}
                                >
                                    Hide Preview
                                </button>
                            </div>
                            <div className="p-6 bg-base-200 font-mono text-sm whitespace-pre-wrap overflow-auto max-h-96">
                                {previewContext}
                            </div>
                        </div>
                    )}

                    {chatResponse && (
                        <div className="mb-6 border border-base-300 rounded-box shadow-lg bg-base-100 overflow-hidden">
                            <div className="bg-primary/10 p-4 border-b border-base-300 flex items-center justify-between">
                                <div className="flex items-center gap-2">
                                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-5 h-5 text-primary">
                                        <path strokeLinecap="round" strokeLinejoin="round" d="M3.75 13.5l10.5-11.25L12 10.5h8.25L9.75 21.75 12 13.5H3.75z" />
                                    </svg>
                                    <h3 className="text-xl font-semibold">AI Response</h3>
                                </div>
                                <button 
                                    className="btn btn-sm btn-outline"
                                    onClick={() => setIsShowingContext(!isShowingContext)}
                                >
                                    {isShowingContext ? 'Hide Context' : 'Show Context'}
                                </button>
                            </div>
                            <div className="p-6 prose max-w-none bg-base-100">
                                <div dangerouslySetInnerHTML={{__html: markdownToHtml(chatResponse)}}/>
                            </div>
                            {isShowingContext && chatContext && (
                                <div className="border-t border-base-300">
                                    <div className="bg-base-200 p-4 border-b border-base-300 flex items-center gap-2">
                                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-5 h-5 text-primary">
                                            <path strokeLinecap="round" strokeLinejoin="round" d="M2.25 12.75V12A2.25 2.25 0 014.5 9.75h15A2.25 2.25 0 0121.75 12v.75m-8.69-6.44l-2.12-2.12a1.5 1.5 0 00-1.061-.44H4.5A2.25 2.25 0 002.25 6v12a2.25 2.25 0 002.25 2.25h15A2.25 2.25 0 0021.75 18V9a2.25 2.25 0 00-2.25-2.25h-5.379a1.5 1.5 0 01-1.06-.44z" />
                                        </svg>
                                        <h3 className="text-lg font-semibold">Context Used</h3>
                                    </div>
                                    <div className="p-6 bg-base-200 font-mono text-sm whitespace-pre-wrap overflow-auto max-h-96">
                                        {chatContext}
                                    </div>
                                </div>
                            )}
                        </div>
                    )}
                </div>
            </Card>
        )
    })

    console.log('Final tabs structure:', tabs)

    return (
        <div className="mx-auto">
            <PageHeader
                title={company.name}
                breadcrumbs={[
                    {label: 'Home', to: '/'},
                    {label: 'Search', to: '/search'},
                    {label: company.name}
                ]}
                actions={[
                    {
                        label: 'Edit',
                        to: `/company/${id}/edit`,
                        variant: 'outline'
                    }
                ]}
            />

            <div className="mb-8">
                <div className="flex items-center gap-4 mb-4">
                    <div className="badge badge-lg badge-info h-full">Industry: {company.industry || 'Unknown'}</div>
                    {company.icp_rating !== undefined &&
                        <div className={`badge badge-lg  h-full ${getIcpRatingClass(company.icp_rating)}`}>
                            ICP Rating: {company.icp_rating}/100
                        </div>}
                    {company.pipedrive_org_id && (
                        <div className="badge badge-lg badge-primary  h-full">In Pipedrive</div>
                    )}
                </div>
            </div>

            {tabs.length > 0 ? (
                <Tabs
                    tabs={tabs}
                    defaultTab="overview"
                    variant="lifted"
                />
            ) : (
                <div className="alert alert-warning">
                    <svg xmlns="http://www.w3.org/2000/svg" className="stroke-current shrink-0 h-6 w-6" fill="none"
                         viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2"
                              d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"/>
                    </svg>
                    <span>No tabs available to display</span>
                </div>
            )}

            {/* Ignore Reason Modal */}
            <Modal
                isOpen={isIgnoreModalOpen}
                onClose={() => setIsIgnoreModalOpen(false)}
                title="Ignore Company"
                footer={
                    <>
                        <button
                            className="btn btn-ghost"
                            onClick={() => setIsIgnoreModalOpen(false)}
                        >
                            Cancel
                        </button>
                        <button
                            className="btn btn-primary"
                            onClick={handleSubmitIgnoreReason}
                            disabled={!ignoreReason.trim()}
                        >
                            Submit
                        </button>
                    </>
                }
            >
                <div className="form-control">
                    <label className="label">
                        <span className="label-text">Please enter a reason for ignoring this company:</span>
                    </label>
                    <textarea
                        className="textarea textarea-bordered h-24"
                        placeholder="Enter reason here..."
                        value={ignoreReason}
                        onChange={(e) => setIgnoreReason(e.target.value)}
                    ></textarea>
                </div>
            </Modal>

            {/* Add Job by URL Modal */}
            <Modal
                isOpen={isAddJobModalOpen}
                onClose={() => setIsAddJobModalOpen(false)}
                title="Add Job by URL"
                footer={
                    <>
                        <button
                            className="btn btn-ghost"
                            onClick={() => setIsAddJobModalOpen(false)}
                        >
                            Cancel
                        </button>
                        <button
                            className="btn btn-primary"
                            onClick={handleAddJob}
                            disabled={!jobUrl.trim()}
                        >
                            Add Job
                        </button>
                    </>
                }
            >
                <div className="form-control">
                    <label className="label">
                        <span className="label-text">Job URL (required):</span>
                    </label>
                    <input
                        type="url"
                        className="input input-bordered"
                        placeholder="https://example.com/job-posting"
                        value={jobUrl}
                        onChange={handleJobUrlChange}
                        required
                    />
                    {jobUrl.trim() && (
                        <div className="text-sm text-info mt-1">
                            <span className="flex items-center">
                                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none"
                                     viewBox="0 0 24 24" stroke="currentColor">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2}
                                          d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                </svg>
                                Job title and description will be automatically parsed from the URL if not provided
                            </span>
                        </div>
                    )}

                    <label className="label mt-2">
                        <span className="label-text">Job Title (optional):</span>
                    </label>
                    <input
                        type="text"
                        className="input input-bordered"
                        placeholder="Will be automatically parsed if left empty"
                        value={jobTitle}
                        onChange={(e) => setJobTitle(e.target.value)}
                    />

                    <label className="label mt-2">
                        <span className="label-text">Job Description (optional):</span>
                    </label>
                    <textarea
                        className="textarea textarea-bordered h-24"
                        placeholder="Will be automatically parsed if left empty"
                        value={jobDescription}
                        onChange={(e) => setJobDescription(e.target.value)}
                    ></textarea>
                </div>
            </Modal>

            {/* Delete Job Confirmation Modal */}
            <Modal
                isOpen={isDeleteJobModalOpen}
                onClose={() => setIsDeleteJobModalOpen(false)}
                title="Confirm Job Deletion"
                footer={
                    <>
                        <button
                            className="btn btn-ghost"
                            onClick={() => setIsDeleteJobModalOpen(false)}
                        >
                            Cancel
                        </button>
                        <button
                            className="btn btn-error"
                            onClick={handleConfirmDeleteJob}
                        >
                            Delete Job
                        </button>
                    </>
                }
            >
                <div className="alert alert-warning mb-4">
                    <svg xmlns="http://www.w3.org/2000/svg" className="stroke-current shrink-0 h-6 w-6" fill="none"
                         viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2"
                              d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"/>
                    </svg>
                    <span>Are you sure you want to delete this job? This action cannot be undone.</span>
                </div>
            </Modal>

            {/* Delete All Jobs Confirmation Modal */}
            <Modal
                isOpen={isDeleteAllJobsModalOpen}
                onClose={() => setIsDeleteAllJobsModalOpen(false)}
                title="Confirm Delete All Jobs"
                footer={
                    <>
                        <button
                            className="btn btn-ghost"
                            onClick={() => setIsDeleteAllJobsModalOpen(false)}
                        >
                            Cancel
                        </button>
                        <button
                            className="btn btn-error"
                            onClick={handleConfirmDeleteAllJobs}
                        >
                            Delete All Jobs
                        </button>
                    </>
                }
            >
                <div className="alert alert-error mb-4">
                    <svg xmlns="http://www.w3.org/2000/svg" className="stroke-current shrink-0 h-6 w-6" fill="none"
                         viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2"
                              d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"/>
                    </svg>
                    <span>Are you sure you want to delete ALL jobs for this company? This action cannot be undone and will remove all job records.</span>
                </div>
            </Modal>

            {/* Crawl Career Page Modal */}
            <Modal
                isOpen={isCrawlCareerPageModalOpen}
                onClose={() => setIsCrawlCareerPageModalOpen(false)}
                title="Crawl Career Page"
                footer={
                    <>
                        <button
                            className="btn btn-ghost"
                            onClick={() => setIsCrawlCareerPageModalOpen(false)}
                        >
                            Cancel
                        </button>
                        <button
                            className="btn btn-primary"
                            onClick={handleCrawlCareerPage}
                            disabled={!careerPageUrl.trim() || isCrawling}
                        >
                            {isCrawling ? (
                                <>
                                    <span className="loading loading-spinner loading-xs mr-2"></span>
                                    Crawling...
                                </>
                            ) : (
                                'Start Crawling'
                            )}
                        </button>
                    </>
                }
            >
                <div className="form-control">
                    <label className="label">
                        <span className="label-text">Career Page URL (required):</span>
                    </label>
                    <input
                        type="url"
                        className="input input-bordered"
                        placeholder="https://example.com/careers"
                        value={careerPageUrl}
                        onChange={(e) => setCareerPageUrl(e.target.value)}
                        required
                    />
                    <div className="text-sm text-info mt-1">
                        <span className="flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none"
                                 viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2}
                                      d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                            </svg>
                            Enter the URL of the company's career page to automatically extract job listings
                        </span>
                    </div>
                </div>
            </Modal>
        </div>
    )
}

export default CompanyDetailPage
