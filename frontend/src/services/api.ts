import axios from 'axios'
import { supabase } from './supabaseClient'

// Get API configuration from environment variables
const API_SERVER = import.meta.env.VITE_API_SERVER || 'http://127.0.0.1:5000'
const API_BASE_PATH = import.meta.env.VITE_API_BASE_URL || '/api'

// Construct the full API URL
const API_BASE_URL = API_SERVER.endsWith('/')
  ? `${API_SERVER}${API_BASE_PATH.startsWith('/') ? API_BASE_PATH.substring(1) : API_BASE_PATH}`
  : `${API_SERVER}${API_BASE_PATH.startsWith('/') ? API_BASE_PATH : `/${API_BASE_PATH}`}`

// Log API configuration in development mode
if (import.meta.env.DEV) {
  console.log('API Configuration:')
  console.log('- Server:', API_SERVER)
  console.log('- Base Path:', API_BASE_PATH)
  console.log('- Full URL:', API_BASE_URL)

  // Example URL that will be used
  const exampleEndpoint = '/companies?search=test'
  console.log('- Example API call will go to:', `${API_BASE_URL}${exampleEndpoint}`)
}

// Create an axios instance with default config
const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json'
  }
})

// Add request interceptor for error handling and authentication
api.interceptors.request.use(
  async (config) => {
    // Get the current session from Supabase
    const { data } = await supabase.auth.getSession()

    // If there's a session with an access token, add it to the request headers
    if (data?.session?.access_token) {
      config.headers.Authorization = `Bearer ${data.session.access_token}`

      // Log in development mode
      if (import.meta.env.DEV) {
        console.log('Adding auth token to request:', config.url)
      }
    } else if (import.meta.env.DEV) {
      console.log('No auth token available for request:', config.url)
    }

    return config
  },
  (error) => Promise.reject(error)
)

// Add response interceptor for error handling
api.interceptors.response.use(
  (response) => response,
  (error) => {
    // Handle common errors
    if (error.response) {
      // Server responded with error status
      console.error('API Error:', error.response.data)

      // Handle specific error codes if needed
    } else if (error.request) {
      // Request made but no response received
      console.error('API Error: No response received', error.request)
    } else {
      // Error setting up request
      console.error('API Error:', error.message)
    }

    return Promise.reject(error)
  }
)

// Define API endpoints
export const jobConfigurationsApi = {
  // Get all job configurations
  getJobConfigurations: () =>
    api.get('/job-configurations'),

  // Get a job configuration by ID
  getJobConfiguration: (jobId: string) => {
    if (!jobId) {
      return Promise.reject(new Error('Job ID is required'));
    }
    return api.get(`/job-configurations/${jobId}`);
  },

  // Create a new job configuration
  createJobConfiguration: (data: any) =>
    api.post('/job-configurations', data),

  // Update a job configuration
  updateJobConfiguration: (jobId: string, data: any) => {
    if (!jobId) {
      return Promise.reject(new Error('Job ID is required'));
    }
    return api.put(`/job-configurations/${jobId}`, data);
  },

  // Delete a job configuration
  deleteJobConfiguration: (jobId: string) => {
    if (!jobId) {
      return Promise.reject(new Error('Job ID is required'));
    }
    return api.delete(`/job-configurations/${jobId}`);
  },

  // Enable a job configuration
  enableJobConfiguration: (jobId: string) => {
    if (!jobId) {
      return Promise.reject(new Error('Job ID is required'));
    }
    return api.post(`/job-configurations/${jobId}/enable`);
  },

  // Disable a job configuration
  disableJobConfiguration: (jobId: string) => {
    if (!jobId) {
      return Promise.reject(new Error('Job ID is required'));
    }
    return api.post(`/job-configurations/${jobId}/disable`);
  },

  // Run a job configuration immediately
  runJobConfiguration: (jobId: string) => {
    if (!jobId) {
      return Promise.reject(new Error('Job ID is required'));
    }
    return api.post(`/job-configurations/${jobId}/run`);
  },
}

export const settingsApi = {
  // Get all settings
  getSettings: () =>
    api.get('/settings'),

  // Get a setting by key
  getSetting: (key: string) =>
    api.get(`/settings/${key}`),

  // Create a new setting
  createSetting: (data: any) =>
    api.post('/settings', data),

  // Update a setting
  updateSetting: (key: string, data: any) =>
    api.put(`/settings/${key}`, data),

  // Delete a setting
  deleteSetting: (key: string) =>
    api.delete(`/settings/${key}`),
}

export const companyApi = {
  // Search companies
  searchCompanies: (query: string) =>
    api.get(`/companies?search=${encodeURIComponent(query)}`),

  // Get company details
  getCompany: (id: number) =>
    api.get(`/companies/${id}`),

  // Preview company before creation
  previewCompany: (name: string) =>
    api.get(`/companies/preview?name=${encodeURIComponent(name)}`),

  // Create new company
  createCompany: (data: any) =>
    api.post('/companies', data),

  // Update company
  updateCompany: (id: number, data: any) =>
    api.put(`/companies/${id}`, data),

  // Search for jobs
  searchJobs: (id: number) =>
    api.post(`/companies/${id}/jobs`),

  // Add job by URL
  addJobByUrl: (id: number, url: string, title?: string, description?: string) =>
    api.post(`/companies/${id}/jobs/add-by-url`, { url, title, description }),

  // Crawl career page
  crawlCareerPage: (id: number, url: string) =>
    api.post(`/companies/${id}/crawl-career-page`, { url }),

  // Reanalyze company
  reanalyzeCompany: (id: number) =>
    api.post(`/companies/${id}/reanalyze`),

  // Reanalyze company without researching description
  reanalyzeCompanySkipDescription: (id: number) =>
    api.post(`/companies/${id}/reanalyze-skip-description`),

  // Research company description
  researchCompanyDescription: (id: number) =>
    api.post(`/companies/${id}/research-description`),

  // Sync with Pipedrive
  syncPipedrive: (id: number) =>
    api.post(`/companies/${id}/sync-pipedrive`),

  // Disconnect from Pipedrive
  disconnectPipedrive: (id: number) =>
    api.post(`/companies/${id}/disconnect-pipedrive`),

  // Sync Pipedrive contacts to local database
  syncPipedriveContacts: (id: number) =>
    api.post(`/companies/${id}/sync-pipedrive-contacts`),

  // Save a single Pipedrive contact to local database
  savePipedriveContact: (companyId: number, pipedriveId: number) =>
    api.post(`/companies/${companyId}/save-pipedrive-contact`, { pipedrive_id: pipedriveId }),

  // Research key decision makers
  researchKeyDecisionMakers: (id: number) =>
    api.post(`/companies/${id}/research-key-decision-makers`),

  // Ignore company
  ignoreCompany: (id: number, reason: string) =>
    api.post(`/companies/${id}/ignore`, { reason }),

  // Unignore company
  unignoreCompany: (id: number) =>
    api.post(`/companies/${id}/unignore`),

  // Check if company has a lead in Pipedrive
  checkLead: (id: number) =>
    api.get(`/companies/${id}/check-lead`),

  // Create a lead in Pipedrive
  createLead: (id: number) =>
    api.post(`/companies/${id}/create-lead`, {}),

  // Archive a lead in Pipedrive
  archiveLead: (id: number, reason?: string) =>
    api.post(`/companies/${id}/archive-lead`, reason ? { reason } : {}),

  // Unarchive a lead in Pipedrive
  unarchiveLead: (id: number) =>
    api.post(`/companies/${id}/unarchive-lead`, {}),

  // Chat with AI about a company using selected jobs and persons as context
  chat: (id: number, query: string, jobIds: number[] = [], personIds: number[] = []) =>
    api.post(`/companies/${id}/chat`, { query, job_ids: jobIds, person_ids: personIds }),

  // Preview context for a company using selected jobs and persons
  previewContext: (id: number, jobIds: number[] = [], personIds: number[] = []) =>
    api.post(`/companies/${id}/preview-context`, { job_ids: jobIds, person_ids: personIds }),

  // Merge companies
  mergeCompanies: (targetCompanyId: number, sourceCompanyId: number) =>
    api.post('/companies/merge', { target_company_id: targetCompanyId, source_company_id: sourceCompanyId }),

  // Update company status
  updateCompanyStatus: (id: number, status: string, comment?: string) =>
    api.put(`/companies/${id}/status`, { status, comment }),

  // Get company workflow history
  getCompanyWorkflowHistory: (id: number) =>
    api.get(`/companies/${id}/workflow-history`),

  // Get all company statuses
  getCompanyStatuses: () =>
    api.get('/companies/statuses'),
}

export const personApi = {
  // Search persons
  searchPersons: (query: string) =>
    api.get(`/persons?search=${encodeURIComponent(query)}`),

  // Get person details
  getPerson: (id: number) =>
    api.get(`/persons/${id}`),

  // Create new person
  createPerson: (data: any) =>
    api.post('/persons', data),

  // Update person
  updatePerson: (id: number, data: any) =>
    api.put(`/persons/${id}`, data),

  // Research person
  researchPerson: (id: number) => {
    // Always force new research
    return api.post(`/persons/${id}/research`)
  },

  // Add person to Pipedrive
  addToPipedrive: (id: number) =>
    api.post(`/persons/${id}/add-to-pipedrive`),

  // Delete person from database and Pipedrive
  deletePerson: (id: number) =>
    api.delete(`/persons/${id}`),

  // Delete person directly from Pipedrive (when not in local database)
  deletePipedrivePerson: (pipedriveId: number) =>
    api.delete(`/pipedrive/persons/${pipedriveId}`)
}

export const jobApi = {
  // Delete job
  deleteJob: (id: number | string) =>
    api.delete(`/jobs/${id}`),

  // Delete all jobs for a company
  deleteAllJobs: (companyId: number) =>
    api.delete(`/companies/${companyId}/delete-all-jobs`),

  // Clean job description
  cleanJobDescription: (id: number | string) =>
    api.post(`/jobs/${id}/clean`)
}

export const generalApi = {
  // Fetch recent updates
  fetchRecentUpdates: (limit: number = 10) =>
    api.get(`/recent-updates?limit=${limit}`)
}

export default api
