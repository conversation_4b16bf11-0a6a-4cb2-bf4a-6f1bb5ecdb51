import React from 'react'
import {Link} from 'react-router-dom'
import './CompanyCard.css'
import {markdownToHtml} from '../../utils/helpers'

export type CompanyCardProps = {
    id: number
    name: string
    industry?: string
    description?: string
    icpRating?: number
    icpDescription?: string
    icpDescriptionPositive?: string
    icpDescriptionNegative?: string
    jobCount?: number
    jobTitles?: Array<[string, string, string?, number?]> // [title, date, url?, id?]
    pipedriveOrgId?: number
    pipedriveOrgName?: string
    pipedriveUrl?: string
    ignored?: boolean
    ignoreReason?: string
    what_they_do?: string
    current_work?: string
    how_to_help?: string
    created_at?: string | Date
    clickable?: boolean
    className?: string
    onReanalyze?: () => void
    onReanalyzeSkipDescription?: () => void
    onResearchDescription?: () => void
    onAddJobByUrl?: () => void
    onSearchJobs?: () => void
    onSyncPipedrive?: () => void
    onResearchKeyDecisionMakers?: () => void
    onIgnore?: () => void
    onUnignore?: () => void
    livePreview?: boolean
}

const CompanyCard: React.FC<CompanyCardProps> = ({
                                                     id,
                                                     name,
                                                     description,
                                                     ignored = false,
                                                     ignoreReason,
                                                     what_they_do,
                                                     current_work,
                                                     how_to_help,
                                                     created_at,
                                                     clickable = true,
                                                     className = '',
                                                     onReanalyze,
                                                     onReanalyzeSkipDescription,
                                                     onResearchDescription,
                                                     onAddJobByUrl,
                                                     onIgnore,
                                                     onUnignore
                                                 }) => {
    return (
        <div
            className={`company-card card bg-base-100 shadow-xl mb-6 relative overflow-visible ${clickable ? 'hover:shadow-2xl transition-shadow duration-200' : ''} ${className}`}>
            {/* Animated Accent Bar */}
            <div className="company-card-accent"></div>

            {/* Glassmorphism Glow Background */}
            <div className="company-card-bg-glow"></div>

            {/* Logo/Avatar Placeholder */}
            <div className="company-card-avatar">
                <span className="company-avatar-placeholder">
                  <svg width="48" height="48" viewBox="0 0 48 48" fill="none">
                    <circle cx="24" cy="24" r="22" fill="#f3f4f6" stroke="#e0e7ef" strokeWidth="4"/>
                    <text x="50%" y="58%" textAnchor="middle" fill="#a1a1aa" fontSize="20"
                          fontFamily="Inter,Arial,sans-serif"
                          dy=".3em">🏢</text>
                  </svg>
                </span>
            </div>

            <div className="card-body p-10 pt-6">
                <div className="flex justify-between items-center mb-4">
                    <div>
                        <h2 className={`card-title text-3xl ${clickable ? 'text-primary' : ''} flex items-center gap-2`}>
                            {clickable ? (
                                <Link to={`/company/${id}`}>{name}</Link>
                            ) : (
                                name
                            )}
                        </h2>
                        {created_at && (
                            <div className="text-sm text-gray-500 mt-1">
                                Created: {new Date(created_at).toLocaleDateString()}
                            </div>
                        )}
                    </div>
                    <div>
                        {ignored ? (
                            <button
                                className="btn btn-outline btn-success"
                                onClick={() => onUnignore && onUnignore()}
                                title={`Unignore company (was ignored because: ${ignoreReason})`}
                                type="button"
                            >
                                <span className="mr-2">✓</span> Unignore
                            </button>
                        ) : (
                            <button
                                className="btn btn-outline btn-error"
                                onClick={() => onIgnore && onIgnore()}
                                title="Ignore this company"
                                type="button"
                            >
                                <span className="mr-2">✕</span> Ignore
                            </button>
                        )}
                    </div>
                </div>

                {/* Company Description */}
                {description && (
                    <>
                        <div className="divider"></div>
                        <div className="flex items-center justify-between mb-4 gap-4 flex-wrap">
                            <h3 className="text-xl font-semibold">Description</h3>
                            <div className="flex gap-2 flex-wrap">
                                <button
                                    className="btn btn-generate"
                                    onClick={() => onReanalyze && onReanalyze()}
                                    title="Regenerate Description and ICP Analysis"
                                    type="button"
                                >
                                    <span className="mr-2">🔄</span> Generate All
                                </button>
                                <button
                                    className="btn btn-generate"
                                    onClick={() => onReanalyzeSkipDescription && onReanalyzeSkipDescription()}
                                    title="Regenerate ICP Analysis without researching company description"
                                    type="button"
                                >
                                    <span className="mr-2">🔄</span> ICP Only
                                </button>
                                <button
                                    className="btn btn-generate"
                                    onClick={() => onResearchDescription && onResearchDescription()}
                                    title="Research company description only"
                                    type="button"
                                >
                                    <span className="mr-2">🔍</span> Research Description
                                </button>
                                <button
                                    className="btn btn-generate"
                                    onClick={() => onAddJobByUrl && onAddJobByUrl()}
                                    title="Add job by URL"
                                    type="button"
                                >
                                    <span className="mr-2">➕</span> Add Job
                                </button>
                            </div>
                        </div>
                        <div className="prose bg-base-200 p-4 rounded-box"
                             dangerouslySetInnerHTML={{__html: markdownToHtml(description)}}/>
                    </>
                )}

                {/* Company Profile */}
                {(what_they_do || current_work || how_to_help) && (
                    <>
                        <div className="divider"></div>
                        <div className="mb-4">
                            <h3 className="text-xl font-semibold mb-4">Profile</h3>

                            {what_they_do && (
                                <div className="mb-4">
                                    <h4 className="text-lg font-medium mb-2">What They Do</h4>
                                    <div className="prose bg-base-200 p-4 rounded-box"
                                         dangerouslySetInnerHTML={{__html: markdownToHtml(what_they_do)}}/>
                                </div>
                            )}

                            {current_work && (
                                <div className="mb-4">
                                    <h4 className="text-lg font-medium mb-2">Current Work</h4>
                                    <div className="prose bg-base-200 p-4 rounded-box"
                                         dangerouslySetInnerHTML={{__html: markdownToHtml(current_work)}}/>
                                </div>
                            )}

                            {how_to_help && (
                                <div className="mb-4">
                                    <h4 className="text-lg font-medium mb-2">How to Help</h4>
                                    <div className="prose bg-base-200 p-4 rounded-box"
                                         dangerouslySetInnerHTML={{__html: markdownToHtml(how_to_help)}}/>
                                </div>
                            )}
                        </div>
                    </>
                )}
            </div>
        </div>
    )
}

export default CompanyCard
