USE suspects;

-- <PERSON>reate enum type for company status
CREATE TABLE IF NOT EXISTS `company_status` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(50) NOT NULL,
  `description` text,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_company_status_name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Insert default statuses
INSERT IGNORE INTO `company_status` (`name`, `description`) VALUES
('NEW', 'Company just came in from search results'),
('QUALIFIED', 'Company has been qualified as a good fit'),
('NO_FIT', 'Company has been qualified as not a good fit'),
('CONTACTED', 'Company has been contacted'),
('NO_REACTION', 'No reaction from company after contact'),
('LOST', 'Company has been marked as lost');

-- Add status_id column to company table
ALTER TABLE company ADD COLUMN status_id bigint unsigned NULL;
ALTER TABLE company ADD CONSTRAINT fk_company_status FOREIGN KEY (status_id) REFERENCES company_status (id);

-- Create workflow transition history table
CREATE TABLE IF NOT EXISTS `company_workflow_transition` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `company_id` bigint unsigned NOT NULL,
  `from_status_id` bigint unsigned NULL,
  `to_status_id` bigint unsigned NOT NULL,
  `comment` text,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `created_by` varchar(255) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `company_id` (`company_id`),
  KEY `from_status_id` (`from_status_id`),
  KEY `to_status_id` (`to_status_id`),
  CONSTRAINT `fk_workflow_company` FOREIGN KEY (`company_id`) REFERENCES `company` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_workflow_from_status` FOREIGN KEY (`from_status_id`) REFERENCES `company_status` (`id`),
  CONSTRAINT `fk_workflow_to_status` FOREIGN KEY (`to_status_id`) REFERENCES `company_status` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Set default status for existing companies
UPDATE company SET status_id = (SELECT id FROM company_status WHERE name = 'NEW') WHERE status_id IS NULL; 