from typing import Optional, List

from pydantic import BaseModel, Field

from backend.src.ai import create_agent
from backend.src.score.icp import get_icp_rating, get_industry
from backend.src.serp.factory import JobSearchProviderFactory
from backend.src.serp.internet import get_company_description
from backend.src.serp.processor import JobProcessorFactory, FullCompanyEnricher
from backend.src.util import db
from backend.src.util.db import (
    update_company, get_connection
)
from backend.src.util.log import log_exception
from backend.src.util.model import Company


def get_company_jobs(company_id: int) -> tuple[int, Optional[List[str]]]:
    """Get job count and titles for a company"""
    try:
        def get_jobs_info(con) -> tuple[int, Optional[List[str]]]:
            with con.cursor() as cursor:
                # Get job count
                cursor.execute(
                    "SELECT COUNT(*) FROM job WHERE company_id = %s",
                    (company_id,)
                )
                count = cursor.fetchone()[0]

                # Get job titles, dates, links, and IDs
                cursor.execute(
                    "SELECT j.id, j.title, j.posted_at, l.link FROM job as j "
                    "LEFT OUTER JOIN link as l ON l.job_id = j.id "
                    "WHERE j.company_id = %s ORDER BY j.posted_at DESC",
                    (company_id,)
                )
                titles = [(row[1], row[2], row[3], row[0]) for row in cursor.fetchall()]

                return count, titles

        return get_connection(get_jobs_info)
    except Exception as e:
        log_exception(f"Error retrieving job count for company {company_id}: {str(e)}", exc_info=True)
        return 0, None


def analyze_company(company: Company, skip_description_research: bool = False) -> None:
    """Reanalyze company description, ICP rating, and ICP description

    Args:
        company: The company to analyze
        skip_description_research: If True, skip researching the company description
    """

    if not skip_description_research:
        # Get fresh company description and industry
        company.description = get_company_description(company.name)
        company.industry = get_industry(company.name, company.description)

    # Get the newest job description if available
    _, job_titles = get_company_jobs(company.id)
    newest_job = None
    if job_titles and len(job_titles) > 0:
        newest_job = job_titles[0][0]  # Get just the title from the (title, date) tuple

    # Recalculate ICP rating and description using newest job
    rating, description = get_icp_rating(company.name, company.description, newest_job, company.tenant_id)

    if description:
        description = description.replace('<pre><code>', '').replace('</code></pre>', '')

    company.icp_rating = rating
    company.icp_description = description

    # Save updates to database
    update_company(company)


def research_company_description(company: Company) -> None:
    """Research and update only the company description and industry

    Args:
        company: The company to research
    """
    # Get fresh company description and industry
    company.description = get_company_description(company.name)
    company.industry = get_industry(company.name, company.description)

    # Save updates to database
    update_company(company)


class CompanyName(BaseModel):
    simple_name: str = Field(description="The simple name of a company")


def create_company_name_agent():
    return create_agent(
        output_type=CompanyName,
        system_prompt=(
            "Entferne alle Rechtsformen oder sonstigen Namensbestandteile, die zu verfälschten Suchergebnissen führen könnten von einem Unternehmensnamen."
        )
    )


def search_company_jobs(company: Company) -> None:
    """Trigger a new job search for the company"""
    company_name = create_company_name_agent().run_sync(company.name).output
    simple_name = company_name.simple_name

    # Get the appropriate job search provider from the factory
    # The factory will determine which provider to use based on environment variables
    job_searcher = JobSearchProviderFactory.create_provider()

    # Create a company enricher
    enricher = FullCompanyEnricher()

    # Create a job processor
    processor = JobProcessorFactory.create_processor("database", company, enricher)

    # Execute the job search
    job_searcher.job_search(simple_name, processor)


def reset_company_jobs(company: Company) -> None:
    db.execute(
        "DELETE FROM suspects.job_keyword WHERE job_keyword.job_id IN (SELECT id FROM job WHERE company_id = %s)",
        (company.id,))
    db.execute("DELETE FROM suspects.link WHERE link.job_id IN (SELECT id FROM job WHERE company_id = %s)",
               (company.id,))
    db.execute("DELETE FROM job WHERE company_id = %s", (company.id,))
