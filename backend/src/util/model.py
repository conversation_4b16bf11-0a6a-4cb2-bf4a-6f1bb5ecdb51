import logging
from datetime import datetime, timedelta
from typing import Optional, List

from pydantic import BaseModel


class Person(BaseModel):
    id: Optional[int] = None
    tenant_id: Optional[int] = None
    pipedrive_id: Optional[int] = None
    company_id: Optional[int] = None
    name: str
    email: Optional[str] = None
    phone: Optional[str] = None
    position: Optional[str] = None
    linkedin: Optional[str] = None
    notes: Optional[str] = None
    research_result: Optional[str] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

    @staticmethod
    def from_row(row):
        return Person(id=row[0], tenant_id=row[1], pipedrive_id=row[2], company_id=row[3], name=row[4], email=row[5],
                      phone=row[6], position=row[7], linkedin=row[8], notes=row[9], research_result=row[10],
                      created_at=row[11] if len(row) > 11 else None,
                      updated_at=row[12] if len(row) > 12 else None)


class CompanyProfile(BaseModel):
    id: Optional[int] = None
    tenant_id: Optional[int] = None
    company_id: int
    what_they_do: Optional[str] = None
    current_work: Optional[str] = None
    how_to_help: Optional[str] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

    @staticmethod
    def from_row(row):
        return CompanyProfile(
            id=row[0],
            tenant_id=row[1],
            company_id=row[2],
            what_they_do=row[3],
            current_work=row[4],
            how_to_help=row[5],
            created_at=row[6] if len(row) > 6 else None,
            updated_at=row[7] if len(row) > 7 else None
        )


class Company(BaseModel):
    id: Optional[int] = None
    tenant_id: Optional[int] = None
    name: str
    score: Optional[float] = None
    outsourcing_likeliness: Optional[float] = None  ## TODO remove
    industry: Optional[str] = None
    icp_rating: Optional[int] = None
    icp_description: Optional[str] = None
    description: Optional[str] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    pipedrive_org_id: Optional[str | int] = None
    ignored: bool = False
    ignore_reason: Optional[str] = None
    status_id: Optional[int] = None
    profile: Optional[CompanyProfile] = None
    job_count: Optional[int] = None
    job_titles: Optional[List[str]] = None

    def __str__(self) -> str:
        return f"{self.name}: {self.industry}\n{self.description}\nICP Rating: {self.icp_rating}\n{self.icp_description}"

    @staticmethod
    def from_row(row):
        return Company(id=row[0],
                       tenant_id=row[1],
                       name=row[2],
                       score=row[3],
                       outsourcing_likeliness=row[4],
                       industry=row[5],
                       icp_rating=row[6],
                       icp_description=row[7],
                       description=row[8],
                       created_at=row[9] if len(row) > 9 else None,
                       updated_at=row[10] if len(row) > 10 else None,
                       pipedrive_org_id=row[11] if len(row) > 11 else None,
                       ignored=row[12] if len(row) > 12 else False,
                       ignore_reason=row[13] if len(row) > 13 else None,
                       status_id=row[14] if len(row) > 14 else None)


class Link(BaseModel):
    tenant_id: Optional[int] = None
    url: str


class Keyword(BaseModel):
    tenant_id: Optional[int] = None
    name: str


class Job(BaseModel):
    id: Optional[int] = None
    tenant_id: Optional[int] = None
    title: str
    company: Company
    posted_at: datetime
    closed_at: Optional[datetime] = None
    created_at: Optional[datetime] = None
    links: list[Link]
    keywords: list[Keyword]
    description: str
    google_job_id: Optional[str] = None
    links: list[Link] = []


class Tenant(BaseModel):
    id: Optional[int] = None
    name: str
    description: Optional[str] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

    @staticmethod
    def from_row(row):
        return Tenant(
            id=row[0],
            name=row[1],
            description=row[2],
            created_at=row[3] if len(row) > 3 else None,
            updated_at=row[4] if len(row) > 4 else None
        )


class User(BaseModel):
    id: Optional[int] = None
    tenant_id: int  # Default to 0 as specified
    email: str
    name: Optional[str] = None
    auth_id: str  # Supabase user ID
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

    @staticmethod
    def from_row(row):
        return User(
            id=row[0],
            tenant_id=row[1],
            email=row[2],
            name=row[3],
            auth_id=row[4],
            created_at=row[5] if len(row) > 5 else None,
            updated_at=row[6] if len(row) > 6 else None
        )


def parse_posted_at(posted_at: str) -> datetime:
    """Parse various date formats into datetime object."""
    try:
        # Try parsing ISO 8601 format with timezone (e.g., 2025-03-25T09:33:27.425Z)
        try:
            # Replace 'Z' with '+00:00' for UTC timezone as fromisoformat doesn't directly support 'Z'
            if 'Z' in posted_at:
                iso_date = posted_at.replace('Z', '+00:00')
                return datetime.fromisoformat(iso_date)
            # Try parsing ISO 8601 format without the Z
            elif 'T' in posted_at:
                return datetime.fromisoformat(posted_at)
        except ValueError:
            pass

        # Try parsing direct date format (YYYY-MM-DD)
        try:
            return datetime.strptime(posted_at, "%Y-%m-%d")
        except ValueError:
            pass

        # Parse German relative time format
        parts = posted_at.split(" ")
        if len(parts) < 2 or not parts[1].isdigit():
            logging.getLogger("usual-suspects").warning(f"Invalid date format: {posted_at}")
            return datetime.now()

        amount = int(parts[1])
        unit = parts[2] if len(parts) > 2 else "Tag"  # Default to "Tag" if unit missing

        if unit in ["Stunden", "Stunde"]:
            return datetime.now() - timedelta(hours=amount)
        elif unit in ["Tagen", "Tag"]:
            return datetime.now() - timedelta(days=amount)
        elif unit in ["Monaten", "Monat"]:
            return datetime.now() - timedelta(days=30 * amount)
        elif unit in ["Jahren", "Jahr"]:
            return datetime.now() - timedelta(days=365 * amount)
        else:
            logging.getLogger("usual-suspects").warning(f"Unknown time unit in: {posted_at}")
            return datetime.now()
    except Exception as e:
        logging.getLogger("usual-suspects").warning(f"Cannot parse date '{posted_at}': {str(e)}")
        return datetime.now()


class CompanyStatus(BaseModel):
    id: Optional[int] = None
    name: str
    description: Optional[str] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

    @staticmethod
    def from_row(row):
        return CompanyStatus(
            id=row[0],
            name=row[1],
            description=row[2],
            created_at=row[3] if len(row) > 3 else None,
            updated_at=row[4] if len(row) > 4 else None
        )


class CompanyWorkflowTransition(BaseModel):
    id: Optional[int] = None
    company_id: int
    from_status_id: Optional[int] = None
    to_status_id: int
    comment: Optional[str] = None
    created_at: Optional[datetime] = None
    created_by: str

    @staticmethod
    def from_row(row):
        return CompanyWorkflowTransition(
            id=row[0],
            company_id=row[1],
            from_status_id=row[2],
            to_status_id=row[3],
            comment=row[4],
            created_at=row[5] if len(row) > 5 else None,
            created_by=row[6] if len(row) > 6 else None
        )
