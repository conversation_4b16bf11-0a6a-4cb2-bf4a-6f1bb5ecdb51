from typing import Optional, List

from backend.src.util.db.company_profiles import get_company_profile_by_company_id, update_company_profile, \
    save_company_profile
from backend.src.util.db.core import get_cursor
from backend.src.util.log import log_info
from backend.src.util.model import Company, CompanyProfile


def get_company_by_id(company_id: int) -> Company:
    """
    Get a company by ID.

    Args:
        company_id: ID of the company to retrieve

    Returns:
        Company: The company with the specified ID, or None if not found
    """

    def select(_, cursor):
        cursor.execute(
            "SELECT id,tenant_id,name,score,outsourcing_likeliness,industry,icp_rating,icp_description,description,created_at,updated_at,pipedrive_id,ignored,ignore_reason,status_id FROM company WHERE id = %s",
            (company_id,))
        result = cursor.fetchall()
        for r in result:
            company = Company.from_row(r)
            company.profile = get_company_profile_by_company_id(company_id)

            return company
        return None

    return get_cursor(select)


def read_company(cursor):
    """
    Read a single company from cursor result and fetch its profile efficiently.
    """
    result = cursor.fetchall()
    if not result:
        return None

    r = result[0]
    company = Company.from_row(r)

    # Get company profile in a more efficient way
    cursor.execute(
        "SELECT id, company_id, what_they_do, current_work, how_to_help "
        "FROM company_profile "
        "WHERE company_id = %s",
        (company.id,)
    )

    profile_row = cursor.fetchone()
    if profile_row:
        profile_id, company_id, what_they_do, current_work, how_to_help = profile_row
        profile = CompanyProfile(
            id=profile_id,
            company_id=company_id,
            what_they_do=what_they_do,
            current_work=current_work,
            how_to_help=how_to_help
        )
        company.profile = profile
        # For backward compatibility
        company.what_they_do = profile.what_they_do
        company.current_work = profile.current_work
        company.how_to_help = profile.how_to_help

    return company


def get_company_by_name(name: str, tenant_id: int) -> Company:
    """
    Get a company by name.

    Args:
        name: Name of the company to retrieve
        tenant_id: Tenant ID for multi-tenancy support

    Returns:
        Company: The company with the specified name, or None if not found
    """

    def select(_, cursor):
        cursor.execute(
            "SELECT id,tenant_id, name,score,outsourcing_likeliness,industry,icp_rating,icp_description,description,created_at,updated_at,pipedrive_id,ignored,ignore_reason FROM company WHERE name = %s AND tenant_id = %s",
            (name, tenant_id))
        return read_company(cursor)

    return get_cursor(select, tenant_id)


def get_company_by_pipedrive_org_id(pipedrive_org_id: int, tenant_id: int) -> Company:
    """
    Get a company by Pipedrive organization ID.

    Args:
        pipedrive_org_id: Pipedrive organization ID of the company to retrieve
        tenant_id: Tenant ID for multi-tenancy support

    Returns:
        Company: The company with the specified Pipedrive organization ID, or None if not found
    """

    def select(_, cursor):
        cursor.execute(
            "SELECT id,tenant_id,name,score,outsourcing_likeliness,industry,icp_rating,icp_description,description,created_at,updated_at,pipedrive_id,ignored,ignore_reason FROM company WHERE pipedrive_id = %s AND tenant_id = %s",
            (pipedrive_org_id, tenant_id))
        return read_company(cursor)

    return get_cursor(select, tenant_id)


def read_companies(cursor):
    """
    Read companies from cursor result and fetch their profiles efficiently.

    This function has been optimized to fetch all company profiles in a single query
    instead of making a separate query for each company.
    """
    result = cursor.fetchall()
    if not result:
        return []

    companies = []
    company_ids = []

    # First create all company objects
    for r in result:
        company = Company.from_row(r)
        companies.append(company)
        company_ids.append(company.id)

    # Then fetch all profiles in a single query
    if company_ids:
        profiles_by_company_id = {}
        placeholders = ', '.join(['%s'] * len(company_ids))
        cursor.execute(f"""
                      SELECT id, company_id, what_they_do, current_work, how_to_help
                      FROM company_profile
                      WHERE company_id IN ({placeholders})
                      """, company_ids)

        for profile_row in cursor.fetchall():
            profile_id, company_id, what_they_do, current_work, how_to_help = profile_row
            profile = CompanyProfile(
                id=profile_id,
                company_id=company_id,
                what_they_do=what_they_do,
                current_work=current_work,
                how_to_help=how_to_help
            )
            profiles_by_company_id[company_id] = profile

        # Assign profiles to companies
        for company in companies:
            profile = profiles_by_company_id.get(company.id)
            if profile:
                company.profile = profile
                # For backward compatibility
                company.what_they_do = profile.what_they_do
                company.current_work = profile.current_work
                company.how_to_help = profile.how_to_help

    return companies


def search_companies_by_name(name: Optional[str], tenant_id: int) -> List[Company]:
    """
    Search for companies by name.

    Args:
        name: Name to search for, or None to get all companies
        tenant_id: Tenant ID for multi-tenancy support

    Returns:
        List[Company]: List of companies matching the search criteria
    """

    def select(_, cursor):
        if name is None:
            cursor.execute(
                "SELECT id,tenant_id,name,score,outsourcing_likeliness,industry,icp_rating,icp_description,description,created_at,updated_at,pipedrive_id,ignored,ignore_reason FROM company WHERE tenant_id = %s",
                (tenant_id,))
        else:
            cursor.execute(
                "SELECT id,tenant_id,name,score,outsourcing_likeliness,industry,icp_rating,icp_description,description,created_at,updated_at,pipedrive_id,ignored,ignore_reason FROM company WHERE name LIKE CONCAT('%', %s, '%') AND tenant_id = %s",
                (name, tenant_id))
        return read_companies(cursor)

    return get_cursor(select, tenant_id)


def update_company(company: Company) -> None:
    """
    Update an existing company in the database.

    Args:
        company: The company to update
    """

    def update(con, cursor):
        if company.description is not None:
            company.description = company.description.replace('<pre><code>', '').replace('</code></pre>', '')
        if company.icp_description is not None:
            company.icp_description = company.icp_description.replace('<pre><code>', '').replace('</code></pre>', '')

        # Update company table (without profile fields)
        cursor.execute(
            "UPDATE company SET name = %s, score = %s, outsourcing_likeliness = %s, description = %s, industry = %s, icp_rating=%s, icp_description=%s, pipedrive_id=%s, ignored=%s, ignore_reason=%s WHERE id = %s AND tenant_id = %s",
            (company.name, company.score, company.outsourcing_likeliness, company.description, company.industry,
             company.icp_rating, company.icp_description, company.pipedrive_org_id, company.ignored,
             company.ignore_reason,
             company.id, company.tenant_id))
        con.commit()

        # Update or create company profile
        if company.profile:
            # If we already have a profile object, update it
            profile = company.profile
            profile.what_they_do = company.what_they_do
            profile.current_work = company.current_work
            profile.how_to_help = company.how_to_help
            update_company_profile(profile)
        else:
            # Create a new profile from the company's profile fields
            profile = get_company_profile_by_company_id(company.id)
            if profile:
                # Update existing profile
                profile.what_they_do = company.what_they_do
                profile.current_work = company.current_work
                profile.how_to_help = company.how_to_help
                update_company_profile(profile)
            else:
                # Create new profile
                profile = CompanyProfile(
                    company_id=company.id,
                    what_they_do=company.what_they_do,
                    current_work=company.current_work,
                    how_to_help=company.how_to_help
                )
                save_company_profile(profile)

    get_cursor(update)


def get_or_create_company(company_name: str, tenant_id: int) -> int:
    """
    Get an existing company ID or create a new company and return its ID.

    Args:
        company_name: Name of the company
        tenant_id: Tenant ID for multi-tenancy support

    Returns:
        int: ID of the existing or newly created company

    Raises:
        ValueError: If company_name is None or empty
    """

    if company_name is None or company_name.strip() == "":
        raise ValueError("Company name cannot be None or empty")

    def select(_, cursor):
        cursor.execute("SELECT id FROM company WHERE name = %s AND tenant_id = %s", (company_name, tenant_id))
        result = cursor.fetchall()
        for r in result:
            return r[0]
        return None

    company_id = get_cursor(select, tenant_id)
    if company_id is None:
        def insert(con, cursor):
            cursor.execute(
                "INSERT INTO company (name, tenant_id) VALUES (%s, %s)",
                (company_name, tenant_id))
            con.commit()
            return cursor.lastrowid

        company_id = get_cursor(insert, tenant_id)
    return company_id


def delete_company(company_id: int) -> None:
    """
    Delete a company and all its related data from the database.

    Args:
        company_id: ID of the company to delete
    """

    def delete(con, cursor):
        # Delete related records first
        cursor.execute(
            "DELETE FROM job_keyword WHERE job_id IN (SELECT id FROM job WHERE company_id = %s)",
            (company_id,))
        cursor.execute("DELETE FROM link WHERE job_id IN (SELECT id FROM job WHERE company_id = %s)",
                       (company_id,))
        cursor.execute("DELETE FROM job WHERE company_id = %s",
                       (company_id,))
        cursor.execute("DELETE FROM company WHERE id = %s ",
                       (company_id,))
        con.commit()
        log_info(f"Deleted company with ID {company_id} and all related data")

    get_cursor(delete)


def get_companies_by_last_update_after(after_date: str, tenant_id: int) -> List[Company]:
    """
    Get companies that were last updated after the specified date.

    Args:
        after_date: Date string in YYYY-MM-DD format
        tenant_id: Tenant ID for multi-tenancy support

    Returns:
        List[Company]: List of companies last updated after the specified date
    """

    def select(_, cursor):
        cursor.execute(
            "SELECT id,tenant_id,name,score,outsourcing_likeliness,industry,icp_rating,icp_description,description,created_at,updated_at,pipedrive_id,ignored,ignore_reason "
            "FROM company WHERE updated_at > %s AND tenant_id = %s",
            (after_date, tenant_id))
        return read_companies(cursor)

    return get_cursor(select, tenant_id)


def get_companies_by_last_update(before_date: str, tenant_id: int) -> List[Company]:
    """
    Get companies that were last updated before the specified date.

    Args:
        before_date: Date string in YYYY-MM-DD format
        tenant_id: Tenant ID for multi-tenancy support

    Returns:
        List[Company]: List of companies last updated before the specified date
    """

    def select(_, cursor):
        cursor.execute(
            "SELECT id,tenant_id,name,score,outsourcing_likeliness,industry,icp_rating,icp_description,description,created_at,updated_at,pipedrive_id,ignored,ignore_reason "
            "FROM company WHERE updated_at < %s AND tenant_id = %s",
            (before_date, tenant_id))
        return read_companies(cursor)

    return get_cursor(select, tenant_id)


def get_recently_updated_companies(tenant_id: int, limit: int = 5) -> List[Company]:
    """
    Get the most recently updated companies.

    Args:
        limit: Maximum number of companies to return
        tenant_id: Tenant ID for multi-tenancy support

    Returns:
        List[Company]: List of most recently updated companies
    """

    def select(_, cursor):
        # Join with company_profile to fetch all data in a single query
        cursor.execute(
            "SELECT c.id, c.tenant_id, c.name, c.score, c.outsourcing_likeliness, c.industry, c.icp_rating, "
            "c.icp_description, c.description, c.created_at, c.updated_at, c.pipedrive_id, c.ignored, c.ignore_reason, "
            "cp.id as profile_id, cp.what_they_do, cp.current_work, cp.how_to_help "
            "FROM company c "
            "LEFT JOIN company_profile cp ON c.id = cp.company_id "
            "WHERE c.tenant_id = %s AND c.ignored = FALSE "
            "ORDER BY c.updated_at DESC "
            "LIMIT %s",
            (tenant_id, limit))

        result = cursor.fetchall()
        companies = []
        for r in result:
            company = Company.from_row(r[:14])  # First 14 columns are company data

            # If profile data exists (profile_id is not None), create profile
            if r[14] is not None:  # profile_id
                profile = CompanyProfile(
                    id=r[14],
                    company_id=company.id,
                    what_they_do=r[15],
                    current_work=r[16],
                    how_to_help=r[17]
                )
                company.profile = profile

            companies.append(company)
        return companies

    return get_cursor(select, tenant_id)


def merge_companies(target_company_id: int, source_company_id: int) -> Company:
    """
    Merge two companies by transferring data from the source company to the target company,
    then deleting the source company.

    Args:
        target_company_id: ID of the company to keep and merge data into
        source_company_id: ID of the company to merge from and then delete

    Returns:
        Company: The updated target company after merging
    """

    # Get both companies
    target_company = get_company_by_id(target_company_id)
    source_company = get_company_by_id(source_company_id)

    if not target_company or not source_company:
        raise ValueError("Both target and source companies must exist")

    def merge(con, cursor):
        # 1. Update target company with source company data if target fields are empty
        if not target_company.description and source_company.description:
            target_company.description = source_company.description

        if not target_company.industry and source_company.industry:
            target_company.industry = source_company.industry

        if (not target_company.icp_rating or target_company.icp_rating == 0) and source_company.icp_rating:
            target_company.icp_rating = source_company.icp_rating
            target_company.icp_description = source_company.icp_description

        # 2. Merge profile data
        if source_company.profile:
            if not target_company.profile:
                # Create profile for target if it doesn't exist
                profile = CompanyProfile(
                    company_id=target_company_id,
                    what_they_do=source_company.what_they_do,
                    current_work=source_company.current_work,
                    how_to_help=source_company.how_to_help
                )
                save_company_profile(profile)
                target_company.profile = profile
                target_company.what_they_do = profile.what_they_do
                target_company.current_work = profile.current_work
                target_company.how_to_help = profile.how_to_help
            else:
                # Update target profile with source data if target fields are empty
                if not target_company.profile.what_they_do and source_company.profile.what_they_do:
                    target_company.profile.what_they_do = source_company.profile.what_they_do
                    target_company.what_they_do = source_company.profile.what_they_do

                if not target_company.profile.current_work and source_company.profile.current_work:
                    target_company.profile.current_work = source_company.profile.current_work
                    target_company.current_work = source_company.profile.current_work

                if not target_company.profile.how_to_help and source_company.profile.how_to_help:
                    target_company.profile.how_to_help = source_company.profile.how_to_help
                    target_company.how_to_help = source_company.profile.how_to_help

                update_company_profile(target_company.profile)

        # 3. Update the target company in the database
        update_company(target_company)

        # 4. Transfer related data from source to target

        # 4.1 Transfer jobs
        cursor.execute("UPDATE job SET company_id = %s WHERE company_id = %s ",
                       (target_company_id, source_company_id))

        # 4.2 Transfer persons
        cursor.execute("UPDATE person SET company_id = %s WHERE company_id = %s ",
                       (target_company_id, source_company_id))

        # 5. Delete the source company
        cursor.execute("DELETE FROM company_profile WHERE company_id = %s ",
                       (source_company_id,))
        cursor.execute("DELETE FROM company WHERE id = %s ",
                       (source_company_id,))

        con.commit()
        log_info(
            f"Merged company {source_company.name} (ID: {source_company_id}) into {target_company.name} (ID: {target_company_id})")

        # Return the updated target company
        return get_company_by_id(target_company_id)

    return get_cursor(merge)


def update_company_status(company_id: int, status_id: int, comment: Optional[str] = None,
                          created_by: str = "system") -> None:
    """
    Update a company's status and create a workflow transition.

    Args:
        company_id: ID of the company to update
        status_id: ID of the new status
        comment: Optional comment for the transition
        created_by: Username of the user making the change
    """
    from backend.src.util.db.company_status import create_workflow_transition
    from backend.src.util.model import CompanyWorkflowTransition

    def update(con, cursor):
        # Get current status
        cursor.execute("SELECT status_id FROM company WHERE id = %s", (company_id,))
        result = cursor.fetchone()
        current_status_id = result[0] if result else None

        # Create workflow transition
        transition = CompanyWorkflowTransition(
            company_id=company_id,
            from_status_id=current_status_id,
            to_status_id=status_id,
            comment=comment,
            created_by=created_by
        )
        create_workflow_transition(transition)

        con.commit()
        log_info(f"Updated company {company_id} status to {status_id}")

    return get_cursor(update)
