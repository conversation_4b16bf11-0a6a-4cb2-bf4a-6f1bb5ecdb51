from typing import List, Optional

from backend.src.util.db.core import get_cursor
from backend.src.util.log import log_info
from backend.src.util.model import CompanyStatus, CompanyWorkflowTransition


def get_company_status_by_id(status_id: int) -> Optional[CompanyStatus]:
    """
    Get a company status by ID.

    Args:
        status_id: ID of the status to retrieve

    Returns:
        CompanyStatus: The status with the specified ID, or None if not found
    """
    def select(_, cursor):
        cursor.execute(
            "SELECT id, name, description, created_at, updated_at FROM company_status WHERE id = %s",
            (status_id,))
        result = cursor.fetchall()
        for r in result:
            return CompanyStatus.from_row(r)
        return None

    return get_cursor(select)


def get_company_status_by_name(name: str) -> Optional[CompanyStatus]:
    """
    Get a company status by name.

    Args:
        name: Name of the status to retrieve

    Returns:
        CompanyStatus: The status with the specified name, or None if not found
    """
    def select(_, cursor):
        cursor.execute(
            "SELECT id, name, description, created_at, updated_at FROM company_status WHERE name = %s",
            (name,))
        result = cursor.fetchall()
        for r in result:
            return CompanyStatus.from_row(r)
        return None

    return get_cursor(select)


def get_all_company_statuses() -> List[CompanyStatus]:
    """
    Get all company statuses.

    Returns:
        List[CompanyStatus]: List of all company statuses
    """
    def select(_, cursor):
        cursor.execute(
            "SELECT id, name, description, created_at, updated_at FROM company_status ORDER BY name")
        return [CompanyStatus.from_row(r) for r in cursor.fetchall()]

    return get_cursor(select)


def create_workflow_transition(transition: CompanyWorkflowTransition) -> CompanyWorkflowTransition:
    """
    Create a new workflow transition and update the company's status.

    Args:
        transition: The workflow transition to create

    Returns:
        CompanyWorkflowTransition: The created workflow transition
    """
    def insert(con, cursor):
        # Insert the workflow transition
        cursor.execute(
            "INSERT INTO company_workflow_transition (company_id, from_status_id, to_status_id, comment, created_by) "
            "VALUES (%s, %s, %s, %s, %s)",
            (transition.company_id, transition.from_status_id, transition.to_status_id, transition.comment,
             transition.created_by))
        transition.id = cursor.lastrowid

        # Update the company's status
        cursor.execute(
            "UPDATE company SET status_id = %s WHERE id = %s",
            (transition.to_status_id, transition.company_id))

        con.commit()
        log_info(f"Created workflow transition for company {transition.company_id}: {transition.from_status_id} -> {transition.to_status_id}")
        return transition

    return get_cursor(insert)


def get_company_workflow_history(company_id: int) -> List[CompanyWorkflowTransition]:
    """
    Get the workflow transition history for a company.

    Args:
        company_id: ID of the company

    Returns:
        List[CompanyWorkflowTransition]: List of workflow transitions for the company
    """
    def select(_, cursor):
        cursor.execute(
            "SELECT id, company_id, from_status_id, to_status_id, comment, created_at, created_by "
            "FROM company_workflow_transition "
            "WHERE company_id = %s "
            "ORDER BY created_at DESC",
            (company_id,))
        return [CompanyWorkflowTransition.from_row(r) for r in cursor.fetchall()]

    return get_cursor(select) 