"""
Company-related API endpoints.
"""

import json

from bs4 import BeautifulSou<PERSON>
from crawler import WebCrawler
from crawler.web.web_graph import WebGraph
from flask import Blueprint, jsonify, request, abort

from backend.src.ai import ApiProxy, MM
from backend.src.company import get_company_jobs, search_company_jobs, analyze_company, reset_company_jobs
from backend.src.pipedrive.api import PipedriveAPI
from backend.src.score.icp import get_industry, get_icp_rating
from backend.src.search import create_company as create_company_search
from backend.src.serp.internet import get_company_description
from backend.src.serp.job_verification import verify_job_posting
from backend.src.serp.person_research import research_key_decision_makers
from backend.src.util.db import (
    search_companies_by_name, get_company_by_id, update_company, get_tenant_by_id,
    merge_companies as db_merge_companies
)
from backend.src.util.db.jobs import get_jobs
from backend.src.util.db.persons import get_persons_by_company_id
from backend.src.util.log import log_error, log_info, log_debug
from backend.src.util.tenant_context import get_current_user_tenant_id
from backend.src.web.api.utils import company_to_dict
from backend.src.web.api_utils import add_contacts
from src.util.db.company_status import get_company_workflow_history, get_company_status_by_id

# Create a Blueprint for company routes
companies_bp = Blueprint('companies', __name__)


@companies_bp.route('/companies', methods=['GET'])
def search_companies():
    """Search companies by name"""
    search_query = request.args.get('search', '')
    if not search_query:
        return jsonify({'companies': []})

    # Get tenant_id from the current user
    tenant_id = get_current_user_tenant_id()

    companies = search_companies_by_name(search_query, tenant_id)
    return jsonify({
        'companies': [company_to_dict(company) for company in companies]
    })


@companies_bp.route('/companies/preview', methods=['GET'])
def preview_company():
    """Preview company before creation"""
    company_name = request.args.get('name', '')
    if not company_name:
        abort(400, description="Company name is required")

    try:
        # Get company description from Google
        google_description = get_company_description(company_name)

        # Get industry
        industry = get_industry(company_name, google_description)

        # Get ICP rating
        rating, description = get_icp_rating(company_name, google_description, None, get_current_user_tenant_id())

        # Format description
        if description:
            # Strip <pre><code> tags before converting to HTML
            description = description.replace('<pre><code>', '').replace('</code></pre>', '')

        # Check if company exists in Pipedrive
        pipedrive_info = None
        pipedrive_url = None
        try:
            api = PipedriveAPI()
            org = api.find_organization_by_name(company_name)
            if org:
                # org is a tuple of (id, name)
                org_id, org_name = org
                pipedrive_info = f"Organization: {org_name} (ID: {org_id})"
                pipedrive_url = f"https://app.pipedrive.com/organization/{org_id}"
        except Exception as e:
            log_error(f"Error checking Pipedrive for {company_name}: {str(e)}", exc_info=True)

        return jsonify({
            'name': company_name,
            'description': google_description,
            'industry': industry,
            'icp_rating': rating,
            'icp_description': description,
            'what_they_do': '',
            'current_work': '',
            'how_to_help': '',
            'pipedrive_info': pipedrive_info,
            'pipedrive_url': pipedrive_url
        })
    except Exception as e:
        log_error(f"Error previewing company {company_name}: {str(e)}", exc_info=True)
        abort(500, description=f"Error previewing company: {str(e)}")


@companies_bp.route('/companies/<int:company_id>', methods=['GET'])
def get_company(company_id):
    """Get company details by ID"""
    company = get_company_by_id(company_id)
    if not company:
        abort(404, description=f"Company with ID {company_id} not found")

    # Get job count and titles
    company.job_count, company.job_titles = get_company_jobs(company_id)

    # Add Pipedrive info without syncing contacts
    add_contacts(company, sync_contacts=False)

    return jsonify(company_to_dict(company))


@companies_bp.route('/companies', methods=['POST'])
def create_company():
    """Create a new company"""
    data = request.json
    if not data or not data.get('name'):
        abort(400, description="Company name is required")

    try:
        company = create_company_search(
            data['name'],
            data.get('description', ''),
            data.get('industry', ''),
            data.get('icp_rating', 0),
            data.get('icp_description', ''),
            data.get('what_they_do', ''),
            data.get('current_work', ''),
            data.get('how_to_help', '')
        )

        return jsonify({
            'message': f"Company '{data['name']}' created successfully",
            'company': company_to_dict(company)
        }), 201
    except Exception as e:
        log_error(f"Error creating company: {str(e)}", exc_info=True)
        abort(500, description=f"Error creating company: {str(e)}")


@companies_bp.route('/companies/<int:company_id>', methods=['PUT'])
def update_company_api(company_id):
    """Update company details"""
    company = get_company_by_id(company_id)
    if not company:
        abort(404, description=f"Company with ID {company_id} not found")

    data = request.json
    if not data:
        abort(400, description="No data provided")

    try:
        # Update company fields
        if 'name' in data:
            company.name = data['name']
        if 'description' in data:
            company.description = data['description']
        if 'industry' in data:
            company.industry = data['industry']
        if 'icp_rating' in data:
            company.icp_rating = int(data['icp_rating'])
        if 'icp_description' in data:
            company.icp_description = data['icp_description']
        if 'what_they_do' in data:
            company.what_they_do = data['what_they_do']
        if 'current_work' in data:
            company.current_work = data['current_work']
        if 'how_to_help' in data:
            company.how_to_help = data['how_to_help']

        # Save to database
        update_company(company)

        return jsonify({
            'message': f"Company '{company.name}' updated successfully",
            'company': company_to_dict(company)
        })
    except Exception as e:
        log_error(f"Error updating company {company_id}: {str(e)}", exc_info=True)
        abort(500, description=f"Error updating company: {str(e)}")


@companies_bp.route('/companies/<int:company_id>/jobs', methods=['POST'])
def search_jobs_api(company_id):
    """Trigger a new job search for the company"""
    company = get_company_by_id(company_id)
    if not company:
        abort(404, description=f"Company with ID {company_id} not found")

    try:
        search_company_jobs(company)

        # Get updated job count and titles
        job_count, job_titles = get_company_jobs(company_id)

        return jsonify({
            'message': f"Job search completed for '{company.name}'",
            'job_count': job_count,
            'job_titles': job_titles
        })
    except Exception as e:
        log_error(f"Error searching jobs for company {company_id}: {str(e)}", exc_info=True)
        abort(500, description=f"Error searching jobs: {str(e)}")


@companies_bp.route('/companies/<int:company_id>/jobs/add-by-url', methods=['POST'])
def add_job_by_url_api(company_id):
    """Add a job by URL for the company"""
    company = get_company_by_id(company_id)
    if not company:
        abort(404, description=f"Company with ID {company_id} not found")

    # Get job URL from request
    data = request.get_json()
    if not data or 'url' not in data:
        abort(400, description="Job URL is required")

    job_url = data['url']
    if not job_url:
        abort(400, description="Job URL cannot be empty")

    try:
        from datetime import datetime
        from backend.src.util.model import Job, Link
        from backend.src.util.db import save_job, job_exists, save_link

        # If title or description is not provided, try to parse them from the URL
        job_title = data.get('title')
        job_description = data.get('description')

        if not job_title or not job_description:
            try:
                from backend.src.serp.job_parser import parse_job_from_url
                log_info(f"Attempting to parse job details from URL: {job_url}")

                job_details = parse_job_from_url(job_url)

                # Only use parsed values if not provided in the request
                if not job_title:
                    job_title = job_details.title
                    log_info(f"Using parsed job title: {job_title}")

                if not job_description:
                    job_description = job_details.description
                    log_info(f"Using parsed job description (first 100 chars): {job_description[:100]}...")
            except Exception as e:
                log_error(f"Error parsing job details from URL: {str(e)}", exc_info=True)
                # Fall back to defaults if parsing fails
                if not job_title:
                    job_title = f"Job at {company.name}"
                if not job_description:
                    job_description = f"Job added manually via URL"

        # Create a job object with the URL
        job = Job(
            title=job_title,
            company=company,
            posted_at=datetime.now(),
            links=[],
            keywords=[],
            description=job_description,
            tenant_id=company.tenant_id
        )

        # Check if the job already exists
        if not job_exists(job):
            # Save the job
            save_job(job)

            # Save the link
            save_link(job.id, Link(url=job_url), job.tenant_id)

            log_info(f"Job added by URL for company {company.name}: {job_url}")
        else:
            log_info(f"Job already exists for URL: {job_url}")

        # Get updated job count and titles
        job_count, job_titles = get_company_jobs(company_id)

        return jsonify({
            'message': f"Job added by URL for '{company.name}'",
            'job_count': job_count,
            'job_titles': job_titles
        })
    except Exception as e:
        log_error(f"Error adding job by URL for company {company_id}: {str(e)}", exc_info=True)
        abort(500, description=f"Error adding job by URL: {str(e)}")


@companies_bp.route('/companies/<int:company_id>/crawl-career-page', methods=['POST'])
def crawl_career_page_api(company_id):
    """Crawl a career page URL for job listings"""
    company = get_company_by_id(company_id)
    if not company:
        abort(404, description=f"Company with ID {company_id} not found")

    # Get career page URL from request
    data = request.get_json()
    if not data or 'url' not in data:
        abort(400, description="Career page URL is required")

    career_page_url = data['url']
    if not career_page_url:
        abort(400, description="Career page URL cannot be empty")

    try:
        log_info(f"Received request to crawl career page for company {company.name}: {career_page_url}")

        # Initialize the web crawler and crawl the career page
        crawler = WebCrawler()
        crawled_data: WebGraph = crawler.crawl(career_page_url, max_depth=1, max_results=50)

        # Extract job offers from the crawled data
        log_info(f"Crawling completed. Extracting job offers from crawled data...")

        # Get all text nodes from the crawled data
        # This is a simplified approach - in a real implementation,
        # you would use more sophisticated methods to identify job listings
        job_offers = []

        for node in crawled_data.all_nodes():
            soup: BeautifulSoup = node.soup
            content = node.to_markdown()
            url = node.url
            title = soup.find('title') or soup.find('h1') or soup.find('h2') or soup.find('h3')
            job_verification = verify_job_posting(title, content, url, company.name)
            if job_verification.is_job_posting and company.name.lower() in job_verification.company_name.lower():
                job_offers.append({
                    'title': title.text if title else 'No title found',
                    'url': url,
                    'description': content
                })
            else:
                log_info(f"Skipping non-job posting: {title.text if title else 'No title found'} / {url}")

        # Get current job count for reference
        from backend.src.company import get_company_jobs
        job_count, _ = get_company_jobs(company_id)

        # Save jobs to database
        from backend.src.util.db.jobs import save_job
        from backend.src.util.model import Job, Link, Keyword
        from datetime import datetime

        jobs_saved = 0
        for job_offer in job_offers:
            # Clean the job description using the MM.clean_job_description method
            raw_description = job_offer['description']
            log_info(f"Cleaning job description for {company.name}")
            cleaned_description = MM.clean_job_description(raw_description)

            # Create a Job object
            job = Job(
                title=job_offer['title'],
                company=company,
                description=cleaned_description,
                posted_at=datetime.now(),  # Using current time as posting time
                links=[Link(url=job_offer['url'])],
                keywords=[],  # No keywords for now,
                tenant_id=company.tenant_id
            )

            # Save the job to the database
            job_id = save_job(job)
            if job_id is not None:
                jobs_saved += 1
                log_info(f"Saved job {job.title} with ID {job_id}")

        return jsonify({
            'message': f"Career page crawl completed for '{company.name}'",
            'url': career_page_url,
            'status': 'completed',
            'current_job_count': job_count,
            'potential_job_offers_found': len(job_offers),
            'jobs_saved': jobs_saved
        })
    except Exception as e:
        log_error(f"Error crawling career page for company {company_id}: {str(e)}", exc_info=True)
        abort(500, description=f"Error crawling career page: {str(e)}")


@companies_bp.route('/companies/<int:company_id>/reanalyze', methods=['POST'])
def reanalyze_company_api(company_id):
    """Reanalyze company description, ICP rating, and ICP description"""
    company = get_company_by_id(company_id)
    if not company:
        abort(404, description=f"Company with ID {company_id} not found")

    try:
        analyze_company(company)

        return jsonify({
            'message': f"Company '{company.name}' reanalyzed successfully",
            'company': company_to_dict(company)
        })
    except Exception as e:
        log_error(f"Error reanalyzing company {company_id}: {str(e)}", exc_info=True)
        abort(500, description=f"Error reanalyzing company: {str(e)}")


@companies_bp.route('/companies/<int:company_id>/reanalyze-skip-description', methods=['POST'])
def reanalyze_company_skip_description_api(company_id):
    """Reanalyze ICP rating and ICP description without researching company description"""
    company = get_company_by_id(company_id)
    if not company:
        abort(404, description=f"Company with ID {company_id} not found")

    try:
        analyze_company(company, skip_description_research=True)

        return jsonify({
            'message': f"Company '{company.name}' reanalyzed successfully (skipped description research)",
            'company': company_to_dict(company)
        })
    except Exception as e:
        log_error(f"Error reanalyzing company {company_id}: {str(e)}", exc_info=True)
        abort(500, description=f"Error reanalyzing company: {str(e)}")


@companies_bp.route('/companies/<int:company_id>/research-description', methods=['POST'])
def research_company_description_api(company_id):
    """Research and update only the company description and industry"""
    company = get_company_by_id(company_id)
    if not company:
        abort(404, description=f"Company with ID {company_id} not found")

    try:
        from backend.src.company import research_company_description
        research_company_description(company)

        return jsonify({
            'message': f"Company '{company.name}' description researched successfully",
            'company': company_to_dict(company)
        })
    except Exception as e:
        log_error(f"Error researching company description {company_id}: {str(e)}", exc_info=True)
        abort(500, description=f"Error researching company description: {str(e)}")


@companies_bp.route('/companies/<int:company_id>/delete-all-jobs', methods=['DELETE'])
def delete_all_jobs_api(company_id):
    """Delete all jobs for a company"""
    company = get_company_by_id(company_id)
    if not company:
        abort(404, description=f"Company with ID {company_id} not found")

    try:
        # Delete all jobs for the company
        reset_company_jobs(company)

        return jsonify({
            'success': True,
            'message': f"All jobs for company '{company.name}' deleted successfully"
        })
    except Exception as e:
        log_error(f"Error deleting all jobs for company {company_id}: {str(e)}", exc_info=True)
        abort(500, description=f"Error deleting all jobs: {str(e)}")


@companies_bp.route('/companies/<int:company_id>/research-key-decision-makers', methods=['POST'])
def research_key_decision_makers_api(company_id):
    """Research key decision makers for a company and save them to the database"""
    company = get_company_by_id(company_id)
    if not company:
        abort(404, description=f"Company with ID {company_id} not found")

    try:
        log_info(f"Researching key decision makers for company: {company.name} (ID: {company_id})")
        decision_makers = research_key_decision_makers(company.name)
        log_info(f"Found {len(decision_makers)} key decision makers for company: {company.name}")

        # Convert decision makers to dict and save to database
        result = []
        saved_count = 0
        for dm in decision_makers:
            # Create dictionary with required fields
            dm_dict = {
                'name': dm.name,
                'position': dm.position,
                'company': dm.company,
                'linkedin': dm.linkedin if hasattr(dm, 'linkedin') else None,
                'email': None,  # PositionInfo doesn't have email attribute
                'phone': None,  # PositionInfo doesn't have phone attribute
                'bio': None  # PositionInfo doesn't have bio attribute
            }

            # If additional_info exists, use it as bio
            if hasattr(dm, 'additional_info') and dm.additional_info:
                dm_dict['bio'] = dm.additional_info

            # Save to database
            try:
                # Create a Person object
                from backend.src.util.model import Person
                person = Person(
                    name=dm.name,
                    position=dm.position,
                    company_id=company_id,  # Associate with the company
                    tenant_id=get_current_user_tenant_id()
                )

                # Convert additional info and sources to JSON for research_result
                research_data = {
                    'additional_info': dm.additional_info if hasattr(dm, 'additional_info') else None,
                    'sources': dm.sources if hasattr(dm, 'sources') else [],
                    'confidence': dm.confidence if hasattr(dm, 'confidence') else None,
                    'linkedin': dm.linkedin if hasattr(dm, 'linkedin') else None
                }
                person.research_result = json.dumps(research_data)

                # Save the person to the database
                from backend.src.util.db import save_person
                save_person(person)
                saved_count += 1

                # Add the database ID to the result
                dm_dict['id'] = person.id
            except Exception as save_error:
                log_error(f"Error saving decision maker {dm.name} to database: {str(save_error)}", exc_info=True)
                # Continue with the next decision maker

            result.append(dm_dict)

        log_info(
            f"Saved {saved_count} out of {len(decision_makers)} decision makers to database for company: {company.name}")
        return jsonify({
            'message': f"Key decision makers researched for '{company.name}'. Saved {saved_count} to database.",
            'decision_makers': result
        })
    except Exception as e:
        log_error(f"Error researching key decision makers for company {company_id}: {str(e)}", exc_info=True)
        abort(500, description=f"Error researching key decision makers: {str(e)}")


@companies_bp.route('/companies/<int:company_id>/ignore', methods=['POST'])
def ignore_company(company_id):
    """Ignore a company and archive its lead in Pipedrive if it exists."""
    company = get_company_by_id(company_id)
    if not company:
        abort(404, description=f"Company with ID {company_id} not found")

    data = request.json
    if not data or not data.get('reason'):
        abort(400, description="Ignore reason is required")

    try:
        # Update company in database
        company.ignored = True
        company.ignore_reason = data.get('reason')
        update_company(company)
        log_info(f"Company '{company.name}' marked as ignored with reason: {company.ignore_reason}")

        # Check if company has a lead in Pipedrive
        if company.pipedrive_org_id:
            log_debug(
                f"Company {company.name} (ID: {company.id}) has Pipedrive organization ID: {company.pipedrive_org_id}")
            log_debug(f"Checking if company {company.name} (ID: {company.id}) has a lead in Pipedrive")
            try:
                api = PipedriveAPI()
                # Check if organization has a lead
                log_debug(f"Calling get_lead_by_org_id({company.pipedrive_org_id}) for company {company.name}")
                lead = api.get_lead_by_org_id(company.pipedrive_org_id)
                if lead:
                    log_debug(
                        f"Found lead for company {company.name} (ID: {company.id}), Pipedrive lead ID: {lead['id']}")
                    # Archive the lead with the ignore reason
                    log_debug(f"Archiving lead with reason: {company.ignore_reason}")
                    api.archive_lead(lead['id'], company.ignore_reason)
                    log_info(f"Archived lead for company '{company.name}' in Pipedrive")
                else:
                    log_debug(f"No lead found for company {company.name} (ID: {company.id}) in Pipedrive")
            except Exception as e:
                log_debug(f"Exception occurred while checking/archiving lead for company {company_id}: {str(e)}")
                log_error(f"Error archiving lead for company {company_id} in Pipedrive: {str(e)}", exc_info=True)
                # Continue even if Pipedrive operation fails

        return jsonify({
            'message': f"Company '{company.name}' ignored successfully",
            'company': company_to_dict(company)
        })
    except Exception as e:
        log_error(f"Error ignoring company {company_id}: {str(e)}", exc_info=True)
        abort(500, description=f"Error ignoring company: {str(e)}")


@companies_bp.route('/companies/<int:company_id>/unignore', methods=['POST'])
def unignore_company(company_id):
    """Unignore a company and unarchive its lead in Pipedrive if it exists."""
    company = get_company_by_id(company_id)
    if not company:
        abort(404, description=f"Company with ID {company_id} not found")

    try:
        # Update company in database
        previous_reason = company.ignore_reason  # Store for logging
        company.ignored = False
        company.ignore_reason = None
        update_company(company)
        log_info(f"Company '{company.name}' unmarked as ignored (previous reason: {previous_reason})")

        # Check if company has a lead in Pipedrive
        if company.pipedrive_org_id:
            log_debug(
                f"Company {company.name} (ID: {company.id}) has Pipedrive organization ID: {company.pipedrive_org_id}")
            log_debug(f"Checking if company {company.name} (ID: {company.id}) has a lead in Pipedrive")
            try:
                api = PipedriveAPI()
                # Check if organization has a lead
                log_debug(f"Calling get_lead_by_org_id({company.pipedrive_org_id}) for company {company.name}")
                lead = api.get_lead_by_org_id(company.pipedrive_org_id)
                if lead:
                    log_debug(
                        f"Found lead for company {company.name} (ID: {company.id}), Pipedrive lead ID: {lead['id']}")
                    # Unarchive the lead
                    log_debug(f"Unarchiving lead for company {company.name} (ID: {company.id})")
                    api.unarchive_lead(lead['id'])
                    log_info(f"Unarchived lead for company '{company.name}' in Pipedrive")
                else:
                    log_debug(f"No lead found for company {company.name} (ID: {company.id}) in Pipedrive")
            except Exception as e:
                log_debug(f"Exception occurred while checking/unarchiving lead for company {company_id}: {str(e)}")
                log_error(f"Error unarchiving lead for company {company_id} in Pipedrive: {str(e)}", exc_info=True)
                # Continue even if Pipedrive operation fails

        return jsonify({
            'message': f"Company '{company.name}' unignored successfully",
            'company': company_to_dict(company)
        })
    except Exception as e:
        log_error(f"Error unignoring company {company_id}: {str(e)}", exc_info=True)
        abort(500, description=f"Error unignoring company: {str(e)}")


def build_company_context(company, job_ids=None, person_ids=None):
    """
    Build context for a company using selected jobs and persons.

    Args:
        company: The company object
        job_ids: List of job IDs to include in context (optional)
        person_ids: List of person IDs to include in context (optional)

    Returns:
        str: The generated context
    """
    # Build context with clearly separated sections
    context = "### TARGET COMPANY ###\n"
    context += f"Company: {company.name}\n"

    if company.description:
        context += f"Description: {company.description}\n"

    if company.industry:
        context += f"Industry: {company.industry}\n"

    if company.icp_rating:
        context += f"ICP Rating: {company.icp_rating}/100\n"

    if company.icp_description:
        context += f"ICP Analysis: {company.icp_description}\n"

    if company.what_they_do:
        context += f"What They Do: {company.what_they_do}\n"

    if company.current_work:
        context += f"Current Work: {company.current_work}\n"

    if company.how_to_help:
        context += f"How To Help: {company.how_to_help}\n"

    # Add selected jobs to context in a separate section
    if job_ids:
        all_jobs = get_jobs(company)
        selected_jobs = [job for job in all_jobs if job.id in job_ids]

        if selected_jobs:
            context += "\n### JOB OFFERS ###\n"
            for job in selected_jobs:
                context += "\n----------------------------\n"
                context += f"- Title: {job.title}\n"
                if job.description:
                    context += f"  Description: {job.description}\n"
                context += "\n"

    # Add selected persons to context in a separate section
    if person_ids:
        all_persons = get_persons_by_company_id(company.id)
        selected_persons = [person for person in all_persons if person.id in person_ids]

        if selected_persons:
            context += "\n### CONTACT PERSONS ###\n"
            for person in selected_persons:
                context += f"- Name: {person.name}\n"
                if person.position:
                    context += f"  Position: {person.position}\n"
                if person.notes:
                    context += f"  Notes: {person.notes}\n"
                if person.research_result:
                    context += f"  Additional Info: {person.research_result}\n"
                context += "\n"

    my_company = get_tenant_by_id(1)
    if my_company:
        context += "\n---------------------------------------\n"
        context += "\n### MY COMPANY ###\n"
        context += f"Name: {my_company.name}\n"
        if my_company.description:
            context += f"Description: {my_company.description}\n"

    return context


@companies_bp.route('/companies/<int:company_id>/preview-context', methods=['POST'])
def preview_company_context(company_id):
    """
    Preview the context that will be used for a chat query.

    Request body should contain:
    - job_ids: List of job IDs to include in context (optional)
    - person_ids: List of person IDs to include in context (optional)
    """
    company = get_company_by_id(company_id)
    if not company:
        abort(404, description=f"Company with ID {company_id} not found")

    # Get request data
    data = request.json or {}
    job_ids = data.get('job_ids', [])
    person_ids = data.get('person_ids', [])

    try:
        # Build context
        context = build_company_context(company, job_ids, person_ids)

        return jsonify({
            'company_id': company_id,
            'company_name': company.name,
            'context': context
        })
    except Exception as e:
        log_error(f"Error previewing context for company {company_id}: {str(e)}", exc_info=True)
        abort(500, description=f"Error previewing context: {str(e)}")


@companies_bp.route('/companies/merge', methods=['POST'])
def merge_companies():
    """
    Merge two companies by transferring data from the source company to the target company,
    then deleting the source company.

    Request body should contain:
    - target_company_id: ID of the company to keep and merge data into
    - source_company_id: ID of the company to merge from and then delete
    """
    data = request.json
    if not data or 'target_company_id' not in data or 'source_company_id' not in data:
        abort(400, description="Both target_company_id and source_company_id are required")

    target_company_id = data['target_company_id']
    source_company_id = data['source_company_id']

    # Validate that both companies exist
    target_company = get_company_by_id(target_company_id)
    source_company = get_company_by_id(source_company_id)

    if not target_company:
        abort(404, description=f"Target company with ID {target_company_id} not found")
    if not source_company:
        abort(404, description=f"Source company with ID {source_company_id} not found")

    try:
        # Merge the companies
        merged_company = db_merge_companies(target_company_id, source_company_id)

        return jsonify({
            'message': f"Successfully merged company '{source_company.name}' into '{target_company.name}'",
            'company': company_to_dict(merged_company)
        })
    except Exception as e:
        log_error(f"Error merging companies: {str(e)}", exc_info=True)
        abort(500, description=f"Error merging companies: {str(e)}")


@companies_bp.route('/companies/<int:company_id>/chat', methods=['POST'])
def company_chat(company_id):
    """
    Chat with an AI about a company using selected jobs and persons as context.

    Request body should contain:
    - query: The user's question
    - job_ids: List of job IDs to include in context (optional)
    - person_ids: List of person IDs to include in context (optional)
    """
    company = get_company_by_id(company_id)
    if not company:
        abort(404, description=f"Company with ID {company_id} not found")

    # Get request data
    data = request.json
    if not data or 'query' not in data:
        abort(400, description="Query is required")

    query = data.get('query')
    job_ids = data.get('job_ids', [])
    person_ids = data.get('person_ids', [])

    try:
        # Build context using the shared function
        context = build_company_context(company, job_ids, person_ids)

        # Create the prompt
        prompt = f"""You are an AI assistant helping with company research and analysis.
Use the following context to answer the user's question.

CONTEXT:
{context}

USER QUESTION:
{query}

Please provide a helpful, informative response based on the context provided. If you don't know the answer or if the information is not in the context, say so rather than making up information."""

        # Send to LLM
        log_info(f"Sending chat query for company {company.name} (ID: {company_id})")
        api = ApiProxy()
        response = api.ask(prompt)

        return jsonify({
            'company_id': company_id,
            'company_name': company.name,
            'query': query,
            'response': response.message,
            'context': context
        })
    except Exception as e:
        log_error(f"Error in company chat for company {company_id}: {str(e)}", exc_info=True)
        abort(500, description=f"Error processing chat request: {str(e)}")


@companies_bp.route('/companies/<int:company_id>/status', methods=['PUT'])
def update_company_status_endpoint(company_id):
    """Update a company's status and create a workflow transition."""
    company = get_company_by_id(company_id)
    if not company:
        abort(404, description=f"Company with ID {company_id} not found")

    data = request.json
    if not data or 'status' not in data:
        abort(400, description="Status is required")

    try:
        # Get status by name
        status = get_company_status_by_name(data['status'])
        if not status:
            abort(400, description=f"Invalid status: {data['status']}")

        # Update company status
        update_company_status(
            company_id=company_id,
            status_id=status.id,
            comment=data.get('comment'),
            created_by=request.headers.get('X-User-Name', 'system')
        )

        return jsonify({
            'message': f"Company status updated to {status.name}",
            'company': company_to_dict(get_company_by_id(company_id))
        })
    except Exception as e:
        log_error(f"Error updating company status: {str(e)}", exc_info=True)
        abort(500, description=f"Error updating company status: {str(e)}")


@companies_bp.route('/companies/<int:company_id>/workflow-history', methods=['GET'])
def get_company_workflow_history_endpoint(company_id):
    """Get a company's workflow transition history."""
    company = get_company_by_id(company_id)
    if not company:
        abort(404, description=f"Company with ID {company_id} not found")

    try:
        transitions = get_company_workflow_history(company_id)
        return jsonify({
            'transitions': [
                {
                    'id': t.id,
                    'from_status': get_company_status_by_id(t.from_status_id).name if t.from_status_id else None,
                    'to_status': get_company_status_by_id(t.to_status_id).name,
                    'comment': t.comment,
                    'created_at': t.created_at.isoformat() if t.created_at else None,
                    'created_by': t.created_by
                }
                for t in transitions
            ]
        })
    except Exception as e:
        log_error(f"Error getting company workflow history: {str(e)}", exc_info=True)
        abort(500, description=f"Error getting company workflow history: {str(e)}")


@companies_bp.route('/companies/statuses', methods=['GET'])
def get_company_statuses_endpoint():
    """Get all available company statuses."""
    try:
        statuses = get_all_company_statuses()
        return jsonify({
            'statuses': [
                {
                    'id': s.id,
                    'name': s.name,
                    'description': s.description
                }
                for s in statuses
            ]
        })
    except Exception as e:
        log_error(f"Error getting company statuses: {str(e)}", exc_info=True)
        abort(500, description=f"Error getting company statuses: {str(e)}")
